/* Overview Content */
.overview-content {
  position: relative;
  z-index: 2;
}

/* Page Header */
.page-header {
  text-align: center;
  padding: 60px 40px 40px;
  max-width: 800px;
  margin: 0 auto;
}

.page-title {
  font-size: 64px;
  font-weight: 600;
  margin: 0 0 24px 0;
  color: #FFFFFF;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
  gap: 20px;
}

.title-with-span {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.title-span {
  max-width: 300px;
  height: auto;
}

.page-subtitle {
  font-size: 18px;
  color: #CCCCCC;
  line-height: 28px;
  margin: 0;
}

/* Dashboard Layout */
.dashboard-layout {
  display: flex;
  gap: 40px;
  padding: 0 40px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Sidebar styles removed - using SideNav component */

/* Dashboard Content Wrapper - Borrowed from UserOverview */
.dashboard-content-wrapper {
  display: flex;
  gap: 40px;
  width: 100%;
}

/* Dashboard Content */
.dashboard-content {
  flex: 1;
  max-width: 920px;
}

/* Section Header */
.overview-header {
  margin-bottom: 16px; /* Reduced spacing to match Daily Transactions */
}

.section-title {
  font-size: 32px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0;
}

.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin: 0 auto 50px auto !important; /* Center the cards grid, remove top margin */
  align-items: start;
  justify-content: center;
  max-width: fit-content;
}

/* Dashboard Cards - Using Daily Transactions card style */
.dashboard-card {
  background: rgba(38, 38, 38, 0.8); /* Match Daily Transactions card background */
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px; /* Match Daily Transactions border radius */
  padding: 16px 20px; /* Match Daily Transactions padding */
  width: 100%;
  min-height: 120px; /* Reduced height to match Daily Transactions style */
  display: flex;
  flex-direction: column;
  gap: 12px; /* Reduced gap */
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  text-align: center; /* Match Daily Transactions center alignment */
}

.dashboard-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(191, 65, 41, 0.3);
}

.card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 8px;
}

.card-title {
  font-family: 'Montserrat', sans-serif; /* Match Daily Transactions font */
  font-size: 12px; /* Match Daily Transactions font size */
  font-weight: 600;
  color: #999999;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0 0 8px 0; /* Match Daily Transactions margin */
  line-height: 16px;
  display: flex;
  align-items: center;
  justify-content: center; /* Center align for consistency */
  gap: 8px;
}

.title-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-value {
  font-family: 'Montserrat', sans-serif; /* Match Daily Transactions font */
  font-size: 24px; /* Match Daily Transactions font size */
  font-weight: 700;
  color: #FFFFFF;
  display: flex;
  align-items: baseline;
  justify-content: center; /* Center align for consistency */
  gap: 8px;
}

.card-unit {
  font-size: 18px;
  font-weight: 500;
  color: #CCCCCC;
}

.card-change {
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.change-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.card-change.positive {
  color: #4CAF50;
}

.card-change.negative {
  color: #FF6B6B;
}



/* Desktop Responsive */
@media (min-width: 1200px) {
  .dashboard-cards {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 769px) and (max-width: 1199px) {
  .dashboard-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {

  .page-header {
    padding: 20px 20px 20px !important; /* Reduced padding for compactness */
  }

  .page-title {
    font-size: 28px !important; /* Significantly reduced title size */
    gap: 8px;
    justify-content: center;
    margin-bottom: 12px !important; /* Reduced margin */
  }

  .title-with-span {
    align-items: center;
  }

  .title-span {
    max-width: 120px; /* Smaller decorative span */
  }

  .page-subtitle {
    font-size: 12px !important; /* Smaller subtitle */
    line-height: 18px !important; /* Tighter line height */
    margin-bottom: -10px !important; /* Add space between subtitle and sidenav */
  }

  .dashboard-layout {
    flex-direction: column;
    padding: 0 20px !important; /* Match treasury dashboard padding */
    gap: 16px !important; /* Reduced gap */
  }

  .dashboard-content-wrapper {
    flex-direction: column;
    gap: 12px !important; /* Reduced gap */
  }

  /* Remove conflicting sidebar styles - SideNav component handles its own mobile styles y*/

  .dashboard-content {
    order: 2;
    max-width: 100%;
    width: 100%;
    padding: 0 px !important; /* Add padding to cards container only */
  }

  .dashboard-cards {
    grid-template-columns: 1fr !important;
    gap: 16px !important; /* Increased card spacing for better alignment */
    margin-top: -20px !important; /* Reduced top margin */
    margin-left: -12px !important; /* Shift cards to the right on mobile */
    margin-right: -60px !important; /* Balance the right shift */
  }

  .dashboard-card {
    width: 105% !important; /* Extended width to left and right */
    max-width: none !important; /* Remove max width constraint */
    padding: 24px 16px 20px 16px !important; /* Increased top padding to push title down */
    min-height: 120px !important; /* Increased height for better spacing */
    border-radius: 12px !important; /* Match Daily Transactions border radius */
    text-align: left !important; /* Left align text for better readability */
    margin-left: -5% !important; /* Extend to the left */
    margin-right: -5% !important; /* Extend to the right */
  }

  .card-title {
    font-size: 12px !important; /* Better readable card titles */
    font-weight: 500 !important;
    margin-top: 8px !important; /* Push title down from top */
    margin-bottom: 12px !important; /* Add spacing below title */
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
  }

  .card-value {
    font-size: 24px !important; /* Larger, more prominent values */
    font-weight: 700 !important;
    margin-bottom: 4px !important; /* Spacing below value */
    line-height: 1.2 !important;
  }

  .card-unit {
    font-size: 14px !important; /* Smaller units */
    opacity: 0.8 !important;
    margin-left: 4px !important; /* Small spacing from value */
  }

  .card-change {
    font-size: 13px !important; /* Change indicators */
    margin-top: 4px !important; /* Spacing above change indicator */
    display: flex !important;
    align-items: center !important;
    gap: 4px !important;
  }

  .card-header {
    margin-bottom: 12px !important; /* Increased spacing on mobile */
    flex-direction: column !important; /* Stack elements vertically on mobile */
    align-items: flex-start !important;
    gap: 4px !important;
  }

  /* Additional mobile optimizations */
  .section-title {
    font-size: 18px !important; /* Smaller section titles */
    margin-bottom: 16px !important; /* Reduced margin */
  }

  .overview-header {
    margin-bottom: 20px !important; /* Reduced header margin */
  }

  /* Ensure proper spacing and prevent clashing */
  .dashboard-mode-tabs {
    margin-bottom: 16px !important; /* Increased margin for better spacing */
    margin-top: 0px !important; /* Remove top margin */
  }

  /* Prevent content from being too close to edges */
  .overview-content {
    padding-bottom: 20px !important; /* Add bottom padding */
  }
}