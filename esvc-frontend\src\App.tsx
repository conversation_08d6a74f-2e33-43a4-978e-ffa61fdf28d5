import { BrowserRouter as Router, Routes, Route, useLocation, Navigate } from 'react-router-dom';
import { useEffect } from 'react';
import './styles/App.css';
import { DemoStateProvider } from './context/DemoStateContext';
import Hero from './components/Hero';
import TreasuryDashboard from './components/TreasuryDashboard';
import ComparisonTable from './components/ComparisonTable';
import FAQ from './components/FAQ';
import SignUp from './components/SignUp';
import ForgotPassword from './components/ForgotPassword';
import VerifyEmail from './components/VerifyEmail';
import Overview from './components/Overview';
import LiveReserve from './components/LiveReserve';
import DailyTransactions from './components/DailyTransactions';
import RealTimeStaking from './components/RealTimeStaking';
import StartupFunding from './components/StartupFunding';
import ROIDistribution from './components/ROIDistribution';
import VisualAnalytics from './components/VisualAnalytics';
import TradeChallenge from './components/TradeChallenge';
import DashboardLayout from './components/DashboardLayout';
import UserOverview from './components/user-dashboard/UserOverview';
import MyStake from './components/user-dashboard/MyStake';
import UserTransactions from './components/user-dashboard/UserTransactions';
import GetFunding from './components/user-dashboard/GetFunding';
import SecuritySettings from './components/user-dashboard/SecuritySettings';
import ChangePassword from './components/user-dashboard/ChangePassword';
import ResetPassword from './components/user-dashboard/ResetPassword';
import TradingDashboard from './components/TradingDashboard';
import TradingDashboardMain from './components/TradingDashboardMain';
import ContactUs from './components/ContactUs';
import { LandingPage as NewLandingPage } from './landing';

// Scroll to top component
const ScrollToTop = () => {
  const location = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location]);

  return null;
};

// Landing Page Component
const LandingPage = () => {
  return (
    <DashboardLayout className="landing-page">
      <Hero />
      <TreasuryDashboard />
      <ComparisonTable />
      <FAQ />
    </DashboardLayout>
  );
};

// Auth Page Component
const AuthPage = ({ children }: { children: React.ReactNode }) => {
  return (
    <DashboardLayout className="auth-page">
      <main className="auth-main">
        {children}
      </main>
    </DashboardLayout>
  );
};

function App() {
  return (
    <DemoStateProvider>
      <Router>
        <ScrollToTop />
        <div className="app">
          <Routes>
          <Route path="/" element={<LandingPage />} />
          <Route path="/signup" element={
            <AuthPage>
              <SignUp initialMode="signup" />
            </AuthPage>
          } />
          <Route path="/login" element={
            <AuthPage>
              <SignUp initialMode="login" />
            </AuthPage>
          } />
          <Route path="/forgot-password" element={
            <AuthPage>
              <ForgotPassword onBack={() => window.history.back()} />
            </AuthPage>
          } />
          <Route path="/verify-email" element={
            <AuthPage>
              <VerifyEmail />
            </AuthPage>
          } />
          <Route path="/overview" element={<Overview />} />
          <Route path="/live-reserve" element={<LiveReserve />} />
          <Route path="/daily-transactions" element={<DailyTransactions />} />
          <Route path="/real-time-staking" element={<RealTimeStaking />} />
          <Route path="/startup-funding" element={<StartupFunding />} />
          <Route path="/roi-distribution" element={<ROIDistribution />} />
          <Route path="/visual-analytics" element={<VisualAnalytics />} />


          
          <Route path="/trade-challenge" element={<TradeChallenge />} />
          <Route path="/trade-challenge-signup" element={<TradingDashboard />} />
          <Route path="/stake-esvc" element={<Navigate to="/user-dashboard/get-funding" replace />} />
          <Route path="/user-dashboard" element={<UserOverview />} />
          <Route path="/user-dashboard/my-stake" element={<MyStake />} />
          <Route path="/user-dashboard/transactions" element={<UserTransactions />} />
          <Route path="/user-dashboard/get-funding" element={<GetFunding />} />
          <Route path="/user-dashboard/security-settings" element={<SecuritySettings />} />
          <Route path="/user-dashboard/change-password" element={<ChangePassword />} />
          <Route path="/user-dashboard/reset-password" element={<ResetPassword />} />
          <Route path="/contact-us" element={<ContactUs />} />
          <Route path="/trading-dashboard" element={<TradingDashboard />} />
          <Route path="/trading-dashboard-main" element={<TradingDashboardMain />} />
          <Route path="/landing" element={<NewLandingPage />} />
        </Routes>
      </div>
    </Router>
    </DemoStateProvider>
  );
}

export default App;
