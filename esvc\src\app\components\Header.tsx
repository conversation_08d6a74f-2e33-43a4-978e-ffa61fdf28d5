"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";

export default function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <header className="bg-neutral-900 mx-auto px-5 pt-6">
      <header className="flex w-full max-w-[1200px] h-[72px] items-center justify-between px-6 py-3 mx-auto bg-neutral-800 rounded-[999px] border-none">
        <Image
          className="w-[167px] h-[38px]"
          alt="ESVC"
          src="/c.animaapp.com/mc62dpc6QTKBF1/img/esvc-2.svg"
          width={167}
          height={38}
        />
        
        {/* Desktop Navigation */}
        <nav className="hidden lg:flex items-center gap-2">
          <Link href="/">
            <div className="inline-flex items-center justify-center px-2 py-1 font-montserrat text-base bg-neutral-700 rounded-[999px] border border-solid border-neutral-600 font-medium text-neutral-100">
              Home
            </div>
          </Link>
          <Link href="/">
            <div className="inline-flex items-center justify-center px-2 py-1 font-montserrat text-base font-normal text-neutral-300 hover:border hover:rounded-[999px] hover:border-neutral-600">
              Stake ESVC
            </div>
          </Link>
          <Link href="/">
            <div className="inline-flex items-center justify-center px-2 py-1 font-montserrat text-base font-normal text-neutral-300 hover:border hover:rounded-[999px] hover:border-neutral-600">
              Get Funding
            </div>
          </Link>
          <Link href="/tradechallenge">
            <div className="inline-flex items-center justify-center px-2 py-1 font-montserrat text-base font-normal text-neutral-300 hover:border hover:rounded-[999px] hover:border-neutral-600">
              Trade Challenge
            </div>
          </Link>
          <Link href="/">
            <div className="inline-flex items-center justify-center px-2 py-1 font-montserrat text-base font-normal text-neutral-300 hover:border hover:rounded-[999px] hover:border-neutral-600">
              Contact Us
            </div>
          </Link>
        </nav>

        {/* Desktop Auth Buttons */}
        <div className="hidden lg:flex items-center gap-[30px]">
          <Link href="/login">
            <button className="inline-flex items-center justify-center gap-2 whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-12 px-4 py-2.5 rounded-[999px] border-neutral-700 font-montserrat font-semibold text-base text-neutral-100">
              Login
            </button>
          </Link>
          <Link href="/login">
            <button className="inline-flex items-center justify-center gap-2 whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 shadow h-12 px-4 py-2.5 rounded-[981.72px] bg-[#bf4129] font-montserrat font-semibold text-base text-neutral-50 hover:bg-[#a83a25]">
              Get Started
            </button>
          </Link>
        </div>

        {/* Mobile Menu Button */}
        <div className="lg:hidden">
          <button 
            className="text-white"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-menu" aria-hidden="true">
              <path d="M4 12h16"></path>
              <path d="M4 18h16"></path>
              <path d="M4 6h16"></path>
            </svg>
          </button>
        </div>

        {/* Mobile Menu */}
        <div className={`fixed top-20 right-0 w-4/5 max-w-sm transform ${isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'} transition-transform duration-300 ease-in-out z-50 p-6 flex flex-col`}>
          <div className="bg-[#262626] rounded-xl p-4 flex flex-col">
            <div className="flex flex-col items-center text-center space-y-4 text-gray-300 text-lg">
              <Link className="w-full bg-[#3a3a3a] text-white py-2 rounded-lg" href="/">Home</Link>
              <Link className="py-2" href="/">Stake ESVC</Link>
              <Link className="py-2" href="/">Get Funding</Link>
              <Link className="py-2" href="/tradechallenge">Trade Challenge</Link>
              <Link className="py-2" href="/">Contact Us</Link>
            </div>
            <div className="flex flex-col space-y-4 mt-4">
              <Link href="/login">
                <button className="bg-[#BF4129] text-white py-3 rounded-lg font-semibold text-lg w-full">Get Started</button>
              </Link>
              <Link href="/login">
                <button className="border border-gray-600 text-white py-3 rounded-lg font-semibold text-lg w-full">Login</button>
              </Link>
            </div>
          </div>
        </div>
      </header>
    </header>
  );
}
