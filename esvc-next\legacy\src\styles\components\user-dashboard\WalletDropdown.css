/* Perfect Wallet Dropdown */
.wallet-dropdown-container {
  position: relative;
  width: 100%;
  z-index: 100;
}

/* Dropdown Trigger */
.wallet-dropdown-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 16px 20px;
  background: #262626;
  border: 2px solid #404040;
  border-radius: 12px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  position: relative;
  overflow: hidden;
}

.wallet-dropdown-trigger::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(191, 65, 41, 0.1) 0%, rgba(191, 65, 41, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.wallet-dropdown-trigger:hover {
  border-color: #BF4129;
  background: #333333;
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(191, 65, 41, 0.2);
}

.wallet-dropdown-trigger:hover::before {
  opacity: 1;
}

.wallet-dropdown-trigger:focus {
  border-color: #BF4129;
  box-shadow: 0 0 0 3px rgba(191, 65, 41, 0.2);
}

.wallet-dropdown-container.open .wallet-dropdown-trigger {
  border-color: #BF4129;
  background: #333333;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

.wallet-dropdown-container.open .wallet-dropdown-trigger::before {
  opacity: 1;
}

/* Trigger Content */
.trigger-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.trigger-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(191, 65, 41, 0.1);
  border-radius: 6px;
  padding: 4px;
}

.trigger-icon img {
  width: 16px;
  height: 16px;
  filter: brightness(0) invert(1);
}

.trigger-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.wallet-label {
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
  line-height: 1.2;
}

.wallet-amount {
  font-size: 13px;
  font-weight: 500;
  color: #CCCCCC;
  line-height: 1.2;
}

.wallet-placeholder {
  font-size: 16px;
  font-weight: 500;
  color: #999999;
}

/* Dropdown Arrow */
.dropdown-arrow {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

.dropdown-arrow img {
  width: 14px;
  height: 14px;
  filter: brightness(0) invert(1);
}

/* Dropdown Menu */
.wallet-dropdown-menu {
  position: absolute;
  top: calc(100% - 2px);
  left: 0;
  right: 0;
  background: #262626;
  border: 2px solid #BF4129;
  border-top: none;
  border-radius: 0 0 12px 12px;
  overflow: hidden;
  z-index: 9999;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  animation: dropdownSlideDown 0.3s ease;
}

@keyframes dropdownSlideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dropdown Options */
.wallet-dropdown-option {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 16px 20px;
  background: none;
  border: none;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  position: relative;
  overflow: hidden;
}

.wallet-dropdown-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(191, 65, 41, 0.1) 0%, rgba(191, 65, 41, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.wallet-dropdown-option:hover {
  background: #333333;
}

.wallet-dropdown-option:hover::before {
  opacity: 1;
}

.wallet-dropdown-option.selected {
  background: rgba(191, 65, 41, 0.1);
  color: #BF4129;
}

.wallet-dropdown-option.selected::before {
  opacity: 1;
}

.wallet-dropdown-option:not(:last-child) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Option Content */
.option-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(191, 65, 41, 0.1);
  border-radius: 6px;
  padding: 4px;
  flex-shrink: 0;
}

.option-icon img {
  width: 16px;
  height: 16px;
  filter: brightness(0) invert(1);
}

.option-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.option-label {
  font-size: 16px;
  font-weight: 600;
  line-height: 1.2;
}

.option-amount {
  font-size: 13px;
  font-weight: 500;
  color: #CCCCCC;
  line-height: 1.2;
}

.wallet-dropdown-option.selected .option-amount {
  color: rgba(191, 65, 41, 0.8);
}

/* Check Icon */
.option-check {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #BF4129;
  flex-shrink: 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .wallet-dropdown-container {
    z-index: 200;
  }

  .wallet-dropdown-trigger {
    padding: 12px 16px;
    border-radius: 8px;
  }

  .trigger-content {
    gap: 10px;
  }

  .trigger-icon {
    width: 20px;
    height: 20px;
    padding: 3px;
  }

  .trigger-icon img {
    width: 14px;
    height: 14px;
  }

  .wallet-label {
    font-size: 14px;
  }

  .wallet-amount {
    font-size: 12px;
  }

  .wallet-placeholder {
    font-size: 14px;
  }

  .dropdown-arrow {
    width: 18px;
    height: 18px;
  }

  .dropdown-arrow img {
    width: 12px;
    height: 12px;
  }

  .wallet-dropdown-menu {
    border-radius: 0 0 8px 8px;
    z-index: 9999;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.6);
  }

  .wallet-dropdown-option {
    padding: 12px 16px;
    gap: 10px;
  }

  .option-icon {
    width: 20px;
    height: 20px;
    padding: 3px;
  }

  .option-icon img {
    width: 14px;
    height: 14px;
  }

  .option-label {
    font-size: 14px;
  }

  .option-amount {
    font-size: 12px;
  }

  .option-check {
    width: 18px;
    height: 18px;
  }
}
