'use client';

import React from 'react';

interface SideNavItem {
  id: string;
  label: string;
  icon: string;
}

type TreasuryTab = 'overview' | 'live-reserve' | 'daily-transactions' | 'real-time-staking' | 'startup-funding' | 'roi-distribution' | 'visual-analytics';

interface TreasurySideNavProps {
  activeTab: TreasuryTab;
  onTabChange: (tabId: TreasuryTab) => void;
}

const TreasurySideNav: React.FC<TreasurySideNavProps> = ({ activeTab, onTabChange }) => {
  const sidebarItems: SideNavItem[] = [
    { id: 'overview', label: 'Overview', icon: 'overview' },
    { id: 'live-reserve', label: 'Live Reserve', icon: 'wallet' },
    { id: 'daily-transactions', label: 'Daily Transactions', icon: 'arrow' },
    { id: 'real-time-staking', label: 'Real-Time Staking', icon: 'card' },
    { id: 'startup-funding', label: 'Startup Funding', icon: 'money' },
    { id: 'roi-distribution', label: 'ROI Distribution', icon: 'percentage' },
    { id: 'visual-analytics', label: 'Visual Analytics', icon: 'chart' }
  ];

  const getIcon = (iconType: string, isActive: boolean) => {
    const color = isActive ? '#F5F5F5' : '#D4D4D4';

    switch (iconType) {
      case 'overview':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <rect x="13.5" y="2" width="7" height="7" stroke={color} strokeWidth="1.5"/>
            <rect x="13.5" y="15" width="7" height="7" stroke={color} strokeWidth="1.5"/>
            <rect x="2" y="11" width="7" height="11" stroke={color} strokeWidth="1.5"/>
            <rect x="2" y="2" width="7" height="7" stroke={color} strokeWidth="1.5"/>
          </svg>
        );
      case 'wallet':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M3.6 7.2h16.8c.88 0 1.6.72 1.6 1.6v11.2c0 .88-.72 1.6-1.6 1.6H3.6c-.88 0-1.6-.72-1.6-1.6V8.8c0-.88.72-1.6 1.6-1.6z" stroke={color} strokeWidth="1.5"/>
            <path d="M3.59 11h5.82" stroke={color} strokeWidth="1.5"/>
            <path d="M3.59 17h5.82" stroke={color} strokeWidth="1.5"/>
            <path d="M3.59 11v6" stroke={color} strokeWidth="1.5"/>
            <circle cx="16.8" cy="14" r="2.4" stroke={color} strokeWidth="1.5"/>
            <path d="M2 3.5h18.4c1.76 0 3.2 1.44 3.2 3.2v11.2" stroke={color} strokeWidth="1.5"/>
          </svg>
        );
      case 'arrow':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M2.25 9h19.5v9h-19.5z" fill={color}/>
            <path d="M2.25 13h19.5v5h-19.5z" fill={color}/>
          </svg>
        );
      case 'card':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M2 17.2h5.2" stroke={color} strokeWidth="1.5"/>
            <path d="M2 13.5h5.2v7.5H2z" stroke={color} strokeWidth="1.5"/>
            <circle cx="15" cy="15" r="3" stroke={color} strokeWidth="1.5"/>
            <path d="M2 2h18.4c1.76 0 3.2 1.44 3.2 3.2v13.6" stroke={color} strokeWidth="1.5"/>
            <path d="M14 2h6.4c1.76 0 3.2 1.44 3.2 3.2v9.6" stroke={color} strokeWidth="1.5"/>
          </svg>
        );
      case 'money':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <circle cx="5.71" cy="8.29" r="3.43" stroke={color} strokeWidth="1.5"/>
            <circle cx="15.43" cy="6.57" r="3.43" stroke={color} strokeWidth="1.5"/>
            <circle cx="7.86" cy="7.86" r="1.14" stroke={color} strokeWidth="1.5"/>
            <path d="M4.78 8.29h1.86" stroke={color} strokeWidth="1.5"/>
            <path d="M16.14 8.29h1.86" stroke={color} strokeWidth="1.5"/>
          </svg>
        );
      case 'percentage':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <rect x="2" y="2" width="20" height="20" stroke={color} strokeWidth="1.5"/>
            <circle cx="8.57" cy="8.73" r="0.65" stroke={color} strokeWidth="1.5"/>
            <path d="M7.77 8.73l8.46 8.46" stroke={color} strokeWidth="1.5"/>
            <circle cx="14.29" cy="13.64" r="0.65" stroke={color} strokeWidth="1.5"/>
          </svg>
        );
      case 'chart':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M2 22h20" stroke={color} strokeWidth="1.5"/>
            <circle cx="10" cy="2" r="0.75" stroke={color} strokeWidth="1.5"/>
            <path d="M3 8h3.5v14H3z" stroke={color} strokeWidth="1.5"/>
            <path d="M17 13h3.5v9H17z" stroke={color} strokeWidth="1.5"/>
          </svg>
        );
      default:
        return null;
    }
  };

  return (
    <>
      {/* Desktop Sidebar */}
      <div className="hidden md:block w-60 flex-shrink-0">
        <div className="bg-[#262626] rounded-2xl p-4">
          <nav className="flex flex-col gap-2">
            {sidebarItems.map((item) => (
              <button
                key={item.id}
                className={`w-full flex items-center gap-3 px-3 py-3 rounded-xl font-montserrat text-sm font-semibold transition-all duration-300 ${
                  activeTab === item.id
                    ? 'bg-[#BF4129] text-[#F5F5F5]'
                    : 'text-[#D4D4D4] hover:text-white hover:bg-neutral-700'
                }`}
                onClick={() => onTabChange(item.id as TreasuryTab)}
              >
                {getIcon(item.icon, activeTab === item.id)}
                <span>{item.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Mobile Horizontal Tabs */}
      <div className="md:hidden w-full mb-4 order-1">
        <div className="bg-[#262626] rounded-2xl p-2 mb-4 mt-0">
          <div className="flex flex-wrap gap-2">
            {sidebarItems.map((item) => (
              <button
                key={item.id}
                className={`px-3 py-2 rounded-xl font-montserrat text-xs font-medium transition-all duration-300 flex items-center gap-1 ${
                  activeTab === item.id
                    ? 'bg-[#BF4129] text-white'
                    : 'text-neutral-300 hover:text-white hover:bg-neutral-700'
                }`}
                onClick={() => onTabChange(item.id as TreasuryTab)}
              >
                {getIcon(item.icon, activeTab === item.id)}
                <span className="hidden sm:inline">{item.label}</span>
              </button>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default TreasurySideNav;
