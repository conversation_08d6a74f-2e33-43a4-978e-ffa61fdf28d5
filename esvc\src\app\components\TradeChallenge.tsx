'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';

type ChallengeStep = 'payment' | 'payment-qr' | 'payment-success' | 'link-account' | 'link-modal' | 'connection-success';

// Copy SVG Component
const CopyIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z" fill="currentColor"/>
  </svg>
);

const TradeChallenge: React.FC = () => {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState<ChallengeStep>('payment');
  const [selectedCurrency, setSelectedCurrency] = useState('');
  const [countdown, setCountdown] = useState(1799); // 29:59 in seconds
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState<'coinbase' | 'binance'>('coinbase');
  const qrCanvasRef = useRef<HTMLCanvasElement>(null);

  const handleStartStaking = () => {
    router.push('/stake-esvc');
  };

  const handleGetFunded = () => {
    router.push('/user-dashboard/get-funding');
  };

  // Countdown timer effect
  useEffect(() => {
    if (currentStep === 'payment-qr' && countdown > 0) {
      const timer = setInterval(() => {
        setCountdown(prev => prev - 1);
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [currentStep, countdown]);

  // Generate QR code when payment QR step is reached
  useEffect(() => {
    if (currentStep === 'payment-qr' && qrCanvasRef.current && selectedCurrency) {
      const walletAddress = 'exeFz9gVzWxMqMzrGpyMWnZRpZBuAkKFef';
      // Simple QR code placeholder - you can add QR code library later
      const canvas = qrCanvasRef.current;
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.fillStyle = '#000';
        ctx.fillRect(0, 0, 200, 200);
        ctx.fillStyle = '#fff';
        ctx.font = '12px Arial';
        ctx.fillText('QR Code', 80, 100);
      }
    }
  }, [currentStep, selectedCurrency]);

  const handleCurrencySelect = (currency: string) => {
    setSelectedCurrency(currency);
    setCurrentStep('payment-qr');
  };

  const handlePaymentComplete = () => {
    setCurrentStep('payment-success');
  };

  const handleProceedToLinkAccount = () => {
    setCurrentStep('link-account');
  };

  const handleGoBack = () => {
    if (currentStep === 'payment-qr') {
      setCurrentStep('payment');
      setSelectedCurrency('');
    } else if (currentStep === 'link-account') {
      setCurrentStep('payment-qr');
    } else if (currentStep === 'link-modal') {
      setCurrentStep('link-account');
      setShowModal(false);
    }
  };

  const handleConnectBot = (e: React.FormEvent) => {
    e.preventDefault();
    setShowModal(false);
    setCurrentStep('connection-success');
  };

  const handleGoToDashboard = () => {
    router.push('/trading-dashboard');
  };

  const handleOpenModal = (type: 'coinbase' | 'binance') => {
    setModalType(type);
    setShowModal(true);
    setCurrentStep('link-modal');
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setCurrentStep('link-account');
  };

  // Format countdown time
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStepStatus = (step: number) => {
    if (currentStep === 'payment' && step === 1) return 'active';
    if (currentStep === 'payment-qr' && step === 1) return 'active';
    if (currentStep === 'payment-success' && step === 1) return 'completed';
    if (currentStep === 'payment-success' && step === 2) return 'active';
    if ((currentStep === 'link-account' || currentStep === 'link-modal') && step === 1) return 'completed';
    if ((currentStep === 'link-account' || currentStep === 'link-modal') && step === 2) return 'completed';
    if ((currentStep === 'link-account' || currentStep === 'link-modal') && step === 3) return 'active';
    if (currentStep === 'connection-success' && step <= 3) return 'completed';
    return 'inactive';
  };

  // Modal blur logic
  const isModalOpen = showModal && currentStep === 'link-modal';
  const isSuccessModalOpen = currentStep === 'payment-success' || currentStep === 'connection-success';

  return (
    <div className="w-full py-20">
      <div className="max-w-6xl mx-auto px-6">
        {/* Go Back Button */}
        {(currentStep === 'payment-qr' || currentStep === 'link-account') && (
          <button 
            className="mb-8 bg-transparent border-none text-[#D19049] font-montserrat text-base font-medium cursor-pointer flex items-center gap-2 transition-colors duration-300 hover:text-[#BF4129]" 
            onClick={handleGoBack}
          >
            <span className="text-lg">←</span> Go Back
          </button>
        )}

        {/* Join ESVC Trade Challenge Section */}
        <div className="text-center mb-16">
          <h1 className="text-white text-5xl font-bold font-montserrat mb-12">Join the ESVC Trade Challenge</h1>

          {/* Progress Steps */}
          <div className="flex items-center justify-center gap-8 mb-16">
            <div className={`flex flex-col items-center gap-3 ${getStepStatus(1) === 'active' ? 'text-[#BF4129]' : getStepStatus(1) === 'completed' ? 'text-green-500' : 'text-neutral-500'}`}>
              <div className={`w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg ${getStepStatus(1) === 'active' ? 'bg-[#BF4129] text-white' : getStepStatus(1) === 'completed' ? 'bg-green-500 text-white' : 'bg-neutral-700 text-neutral-400'}`}>
                {getStepStatus(1) === 'completed' ? '✓' : '1'}
              </div>
              <span className="text-sm font-semibold font-montserrat">PAY ONE-TIME FEE</span>
            </div>
            
            <div className={`w-16 h-0.5 ${getStepStatus(2) === 'completed' || getStepStatus(3) === 'completed' ? 'bg-green-500' : getStepStatus(2) === 'active' ? 'bg-[#BF4129]' : 'bg-neutral-700'}`}></div>
            
            <div className={`flex flex-col items-center gap-3 ${getStepStatus(2) === 'active' ? 'text-[#BF4129]' : getStepStatus(2) === 'completed' ? 'text-green-500' : 'text-neutral-500'}`}>
              <div className={`w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg ${getStepStatus(2) === 'active' ? 'bg-[#BF4129] text-white' : getStepStatus(2) === 'completed' ? 'bg-green-500 text-white' : 'bg-neutral-700 text-neutral-400'}`}>
                {getStepStatus(2) === 'completed' ? '✓' : '2'}
              </div>
              <span className="text-sm font-semibold font-montserrat">LINK YOUR TRADING ACCOUNT</span>
            </div>
            
            <div className={`w-16 h-0.5 ${getStepStatus(3) === 'completed' ? 'bg-green-500' : getStepStatus(3) === 'active' ? 'bg-[#BF4129]' : 'bg-neutral-700'}`}></div>
            
            <div className={`flex flex-col items-center gap-3 ${getStepStatus(3) === 'active' ? 'text-[#BF4129]' : getStepStatus(3) === 'completed' ? 'text-green-500' : 'text-neutral-500'}`}>
              <div className={`w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg ${getStepStatus(3) === 'active' ? 'bg-[#BF4129] text-white' : getStepStatus(3) === 'completed' ? 'bg-green-500 text-white' : 'bg-neutral-700 text-neutral-400'}`}>
                {getStepStatus(3) === 'completed' ? '✓' : '3'}
              </div>
              <span className="text-sm font-semibold font-montserrat">YOUR TRADING DASHBOARD</span>
            </div>
          </div>

          {/* Step Content */}
          {currentStep === 'payment' && (
            <div className="bg-[#262626] rounded-2xl p-8 max-w-2xl mx-auto">
              <h2 className="text-white text-3xl font-bold font-montserrat mb-4">Pay Your $100 Entry Fee</h2>
              <p className="text-neutral-300 text-lg font-montserrat mb-8">
                Join the challenge by paying a one-time subscription fee of $100 (USDC equivalent).
                Choose your preferred currency to proceed.
              </p>

              <div className="space-y-4">
                <label className="block text-white text-sm font-semibold font-montserrat text-left">Choose your payment currency</label>
                <select
                  className="w-full h-12 bg-neutral-800 border border-neutral-700 rounded-full px-5 text-white font-montserrat text-base transition-all duration-300 focus:outline-none focus:border-[#BF4129] focus:ring-1 focus:ring-[#BF4129]"
                  value={selectedCurrency}
                  onChange={(e) => handleCurrencySelect(e.target.value)}
                >
                  <option value="">Click to select</option>
                  <option value="USDC">USDC</option>
                  <option value="Bitcoin">Bitcoin</option>
                  <option value="Ethereum">Ethereum</option>
                </select>
              </div>
            </div>
          )}

          {currentStep === 'payment-qr' && (
            <div className="bg-[#262626] rounded-2xl p-8 max-w-2xl mx-auto">
              <h2 className="text-white text-3xl font-bold font-montserrat mb-4">Pay Your $100 Entry Fee</h2>
              <p className="text-neutral-300 text-lg font-montserrat mb-8">
                Join the challenge by paying a one-time subscription fee of $100 (USDC equivalent).
                Choose your preferred currency to proceed.
              </p>

              <div className="space-y-6">
                <div className="space-y-4">
                  <label className="block text-white text-sm font-semibold font-montserrat text-left">Choose your payment currency</label>
                  <div className="w-full h-12 bg-neutral-800 border border-neutral-700 rounded-full px-5 flex items-center text-white font-montserrat text-base">
                    {selectedCurrency}
                  </div>
                </div>

                <div className="flex flex-col items-center space-y-6">
                  <canvas ref={qrCanvasRef} className="w-48 h-48 bg-white rounded-lg" width="200" height="200" />

                  <div className="w-full space-y-2">
                    <label className="block text-white text-sm font-semibold font-montserrat text-left">USDC Wallet Address</label>
                    <div className="flex items-center gap-2 w-full h-12 bg-neutral-800 border border-neutral-700 rounded-full px-5">
                      <span className="text-white font-montserrat text-sm flex-1 truncate">exeFz9gVzWxMqMzrGpyMWnZRpZBuAkKFef</span>
                      <button
                        className="text-neutral-400 hover:text-white transition-colors duration-300"
                        onClick={() => {
                          navigator.clipboard.writeText('exeFz9gVzWxMqMzrGpyMWnZRpZBuAkKFef');
                        }}
                      >
                        <CopyIcon />
                      </button>
                    </div>
                  </div>

                  <div className="text-[#BF4129] text-2xl font-bold font-montserrat">
                    {formatTime(countdown)}
                  </div>

                  <div className="flex items-center gap-2 text-neutral-300 text-sm font-montserrat">
                    <span className="text-blue-400">ℹ</span>
                    Do not close this page until deposit is complete.
                  </div>

                  <button 
                    className="h-12 bg-[#BF4129] hover:bg-[#a83a25] border-none rounded-full text-white font-montserrat text-base font-semibold cursor-pointer transition-all duration-300 px-8"
                    onClick={handlePaymentComplete}
                  >
                    I Have Deposited
                  </button>
                </div>
              </div>
            </div>
          )}

          {currentStep === 'link-account' && (
            <div className="bg-[#262626] rounded-2xl p-8 max-w-4xl mx-auto">
              <h2 className="text-white text-3xl font-bold font-montserrat mb-4">Link Your Trading Account</h2>
              <p className="text-neutral-300 text-lg font-montserrat mb-8">
                Choose the exchange you'd like to connect and enter your API credentials below. This
                enables the trading bot to trade on your behalf securely.
              </p>

              <div className="flex items-center gap-3 bg-blue-500/10 border border-blue-500/30 rounded-xl p-4 mb-6">
                <div className="text-blue-400 text-xl">ℹ</div>
                <p className="text-neutral-300 text-sm font-montserrat">
                  This is 100% safe as we don't have access to sight or withdrawals on your Binance or Coinbase Account.
                  We only use the trade API.
                </p>
              </div>

              <div className="mb-8">
                <button className="flex items-center gap-2 text-[#BF4129] font-montserrat text-sm font-medium hover:text-[#a83a25] transition-colors duration-300">
                  <span className="text-lg">▶</span>
                  Watch: How to Get Your API Key
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <button
                  className="bg-neutral-800 hover:bg-neutral-700 border border-neutral-600 rounded-xl p-6 text-left transition-all duration-300 group"
                  onClick={() => handleOpenModal('coinbase')}
                >
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-xl">C</div>
                    <div className="flex-1">
                      <div className="text-white font-semibold font-montserrat">Coinbase API</div>
                      <div className="text-neutral-400 text-sm font-montserrat">Trade using Coinbase API</div>
                    </div>
                    <div className="text-[#BF4129] font-montserrat font-medium group-hover:text-[#a83a25]">Link Coinbase</div>
                  </div>
                </button>

                <button
                  className="bg-neutral-800 hover:bg-neutral-700 border border-neutral-600 rounded-xl p-6 text-left transition-all duration-300 group"
                  onClick={() => handleOpenModal('binance')}
                >
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center text-white font-bold text-xl">B</div>
                    <div className="flex-1">
                      <div className="text-white font-semibold font-montserrat">Binance API</div>
                      <div className="text-neutral-400 text-sm font-montserrat">Trade using Binance API</div>
                    </div>
                    <div className="text-[#BF4129] font-montserrat font-medium group-hover:text-[#a83a25]">Link Binance</div>
                  </div>
                </button>
              </div>
            </div>
          )}
        </div>

        {/* More Than Trading Section */}
        <div className="bg-[#262626] rounded-2xl p-8 text-center">
          <h2 className="text-white text-3xl font-bold font-montserrat mb-4">More Than Trading. Fueling Innovation</h2>
          <p className="text-neutral-300 text-lg font-montserrat mb-8 max-w-4xl mx-auto">
            Your trading journey with us is just the beginning. Stake ESVC to earn daily ROI and unlock the
            chance to pitch your own startup ideas for funding. We reinvest a portion of our platform's profit to
            support bold solutions from our staking community.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button 
              className="h-12 bg-[#BF4129] hover:bg-[#a83a25] border-none rounded-full text-white font-montserrat text-base font-semibold cursor-pointer transition-all duration-300 px-8 flex items-center justify-center gap-2"
              onClick={handleStartStaking}
            >
              Start Staking Now
              <span className="text-lg">🚀</span>
            </button>
            <button 
              className="h-12 bg-transparent border border-[#BF4129] rounded-full text-[#BF4129] hover:bg-[#BF4129] hover:text-white font-montserrat text-base font-semibold cursor-pointer transition-all duration-300 px-8"
              onClick={handleGetFunded}
            >
              Get Funded
            </button>
          </div>
        </div>
      </div>

      {/* API Key Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <div className="bg-[#262626] rounded-2xl p-8 max-w-md w-full">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-white text-xl font-bold font-montserrat">
                Link Your {modalType === 'coinbase' ? 'Coinbase' : 'Binance'}
              </h3>
              <button 
                className="text-neutral-400 hover:text-white text-2xl"
                onClick={handleCloseModal}
              >
                ×
              </button>
            </div>
            
            <p className="text-neutral-300 text-sm font-montserrat mb-6">
              Copy your API keys from your {modalType === 'coinbase' ? 'Coinbase.com' : 'Binance.com'} account and paste here.
              You can only copy this from {modalType === 'coinbase' ? 'Coinbase.com' : 'Binance.com'} website and not the {modalType === 'coinbase' ? 'Coinbase' : 'Binance'} App.
            </p>
            
            <form className="space-y-4" onSubmit={handleConnectBot}>
              <div className="space-y-2">
                <label className="block text-white text-sm font-semibold font-montserrat">API Key</label>
                <input
                  type="text"
                  className="w-full h-12 bg-neutral-800 border border-neutral-700 rounded-full px-5 text-white font-montserrat text-base transition-all duration-300 focus:outline-none focus:border-[#BF4129] focus:ring-1 focus:ring-[#BF4129] placeholder-neutral-500"
                  placeholder="Enter API Key"
                />
              </div>
              
              <div className="space-y-2">
                <label className="block text-white text-sm font-semibold font-montserrat">Secret Key</label>
                <input
                  type="password"
                  className="w-full h-12 bg-neutral-800 border border-neutral-700 rounded-full px-5 text-white font-montserrat text-base transition-all duration-300 focus:outline-none focus:border-[#BF4129] focus:ring-1 focus:ring-[#BF4129] placeholder-neutral-500"
                  placeholder="Enter Secret Key"
                />
              </div>
              
              <div className="space-y-2">
                <label className="block text-white text-sm font-semibold font-montserrat">Trading Capital ($)</label>
                <input
                  type="number"
                  className="w-full h-12 bg-neutral-800 border border-neutral-700 rounded-full px-5 text-white font-montserrat text-base transition-all duration-300 focus:outline-none focus:border-[#BF4129] focus:ring-1 focus:ring-[#BF4129] placeholder-neutral-500"
                  placeholder="1000"
                />
              </div>
              
              <div className="flex items-center gap-3 bg-blue-500/10 border border-blue-500/30 rounded-xl p-3">
                <div className="text-blue-400 text-lg">ℹ</div>
                <p className="text-neutral-300 text-xs font-montserrat">
                  The amount you want the bot to trade with. Make sure this amount is available in USDT in
                  your linked exchange account.
                </p>
              </div>
              
              <button 
                type="submit" 
                className="w-full h-12 bg-[#BF4129] hover:bg-[#a83a25] border-none rounded-full text-white font-montserrat text-base font-semibold cursor-pointer transition-all duration-300"
              >
                Connect to Bot
              </button>
            </form>
          </div>
        </div>
      )}

      {/* Payment Success Modal */}
      {currentStep === 'payment-success' && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <div className="bg-[#262626] rounded-2xl p-8 max-w-md w-full text-center">
            <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <div className="text-white text-2xl font-bold">✓</div>
            </div>
            <h2 className="text-white text-2xl font-bold font-montserrat mb-4">
              Your ${selectedCurrency === 'USDT' ? '100' : selectedCurrency === 'BTC' ? '0.0015' : '0.04'} payment has been received successfully.
            </h2>
            <p className="text-neutral-300 font-montserrat mb-6">
              You're now one step closer to unlocking automated trading with the ESVC Trade Bot. Let's connect your preferred exchange to get started.
            </p>
            <button 
              className="h-12 bg-[#BF4129] hover:bg-[#a83a25] border-none rounded-full text-white font-montserrat text-base font-semibold cursor-pointer transition-all duration-300 px-8"
              onClick={handleProceedToLinkAccount}
            >
              Proceed to Connect Exchange
            </button>
          </div>
        </div>
      )}

      {/* Connection Success Modal */}
      {currentStep === 'connection-success' && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <div className="bg-[#262626] rounded-2xl p-8 max-w-md w-full text-center">
            <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <div className="text-white text-2xl font-bold">✓</div>
            </div>
            <h2 className="text-white text-2xl font-bold font-montserrat mb-4">
              Your trading account is now securely linked to the ESVC Trade Bot.
            </h2>
            <p className="text-neutral-300 font-montserrat mb-6">
              We'll begin monitoring your account and executing trades once capital is detected in your exchange.
              Make sure you've funded your exchange wallet with USDT and a little extra for fees.
            </p>
            <button 
              className="h-12 bg-[#BF4129] hover:bg-[#a83a25] border-none rounded-full text-white font-montserrat text-base font-semibold cursor-pointer transition-all duration-300 px-8"
              onClick={handleGoToDashboard}
            >
              Go to Trade Bot Dashboard
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default TradeChallenge;
