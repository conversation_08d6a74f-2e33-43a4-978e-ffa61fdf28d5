/* Stake Modal Overlay */
.stake-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999;
  padding: 20px;
}

/* Stake Modal Container */
.stake-modal {
  background: #262626;
  border-radius: 20px;
  padding: 32px;
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  border: 1px solid #404040;
}

.stake-modal-close {
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  color: #CCCCCC;
  font-size: 24px;
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.stake-modal-close:hover {
  background: #404040;
  color: #FFFFFF;
}

/* Modal Title */
.stake-modal-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 24px 0;
  text-align: center;
}

/* Form Styles */
.stake-form-group {
  margin-bottom: 24px;
}

.stake-form-label {
  display: block;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
  margin-bottom: 8px;
}

.stake-form-description {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #888888;
  margin: 4px 0 8px 0;
  font-weight: 400;
}

/* Stake Amount Input */
.stake-amount-input {
  position: relative;
  display: flex;
  align-items: center;
}

.currency-symbol {
  position: absolute;
  left: 16px;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  color: #CCCCCC;
  font-weight: 600;
  z-index: 1;
}

.stake-input {
  width: 100%;
  padding: 16px 20px 16px 40px;
  background: #1A1A1A;
  border: 1px solid #404040;
  border-radius: 12px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 500;
  outline: none;
  transition: border-color 0.3s ease;
}

.stake-input:focus {
  border-color: #BF4129;
}

/* ESVC Received Display */
.esvc-received {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: #1A1A1A;
  border: 1px solid #404040;
  border-radius: 12px;
  position: relative;
}

.esvc-amount {
  font-family: 'Montserrat', sans-serif;
  font-size: 18px;
  font-weight: 700;
  color: #FFFFFF;
}

.esvc-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #F59E0B;
}

.swap-icon {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(245, 158, 11, 0.1);
  border-radius: 8px;
  border: 1px solid #F59E0B;
}

.swap-arrow {
  width: 16px;
  height: 16px;
  filter: brightness(0) saturate(100%) invert(77%) sepia(89%) saturate(1919%) hue-rotate(359deg) brightness(101%) contrast(94%);
}

/* Duration Options */
.duration-options {
  display: flex;
  gap: 16px;
}

.duration-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.duration-option input[type="radio"] {
  width: 20px;
  height: 20px;
  accent-color: #BF4129;
}

.duration-text {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  color: #FFFFFF;
  font-weight: 500;
}

/* Wallet Info */
.wallet-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.info-icon {
  color: #888888;
  font-size: 16px;
}

.info-text {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #888888;
  font-weight: 400;
}

/* Buttons */
.proceed-payment-btn,
.deposit-complete-btn,
.proceed-dashboard-btn {
  width: 100%;
  padding: 16px 24px;
  background: #BF4129;
  border: none;
  border-radius: 12px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 8px;
}

.proceed-payment-btn:hover,
.deposit-complete-btn:hover,
.proceed-dashboard-btn:hover {
  background: #A63622;
  transform: translateY(-2px);
}

.proceed-payment-btn:disabled {
  background: #404040;
  cursor: not-allowed;
  transform: none;
}

/* Back Button */
.back-btn {
  background: none;
  border: none;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  margin-bottom: 24px;
  transition: color 0.3s ease;
}

.back-btn:hover {
  color: #FFFFFF;
}

/* Currency Selection */
.currency-label {
  display: block;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
  margin-bottom: 12px;
}

.currency-select {
  width: 100%;
  padding: 16px 20px;
  background: #1A1A1A;
  border: 1px solid #404040;
  border-radius: 12px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  outline: none;
  margin-bottom: 24px;
}

.currency-select:focus {
  border-color: #BF4129;
}

/* Payment Warning */
.payment-warning {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.3);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 24px;
}

.warning-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.warning-text strong {
  display: block;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 700;
  color: #FFFFFF;
  margin-bottom: 8px;
}

.warning-text p {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #CCCCCC;
  margin: 4px 0;
  font-weight: 400;
}

/* QR Payment Section */
.currency-display {
  text-align: center;
  margin-bottom: 24px;
}

.selected-currency {
  display: inline-block;
  padding: 8px 16px;
  background: #1A1A1A;
  border: 1px solid #404040;
  border-radius: 8px;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
}

/* QR Code */
.qr-code-container {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

.qr-code {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.qr-canvas {
  border-radius: 8px;
}

.qr-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qr-squares {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 2px;
  width: 120px;
  height: 120px;
}

.qr-square {
  background: #FFFFFF;
  border-radius: 2px;
}

.qr-square.filled {
  background: #000000;
}

/* Wallet Address Display */
.wallet-address-section {
  margin-bottom: 24px;
}

.wallet-label {
  display: block;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
  margin-bottom: 8px;
}

.wallet-address-display {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #1A1A1A;
  border: 1px solid #404040;
  border-radius: 8px;
}

.wallet-address {
  flex: 1;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #CCCCCC;
  font-weight: 400;
  word-break: break-all;
}

.copy-btn {
  background: none;
  border: none;
  color: #F59E0B;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.copy-btn:hover {
  color: #FFFFFF;
}

.copy-btn svg {
  width: 16px;
  height: 16px;
  color: #F59E0B;
}

.copy-btn:hover svg {
  color: #FFFFFF;
}

/* Payment Timer */
.payment-timer {
  text-align: center;
  margin-bottom: 16px;
}

.timer-display {
  display: inline-block;
  padding: 12px 24px;
  background: #F59E0B;
  border-radius: 8px;
  font-family: 'Montserrat', sans-serif;
  font-size: 20px;
  font-weight: 700;
  color: #000000;
}

/* Payment Info */
.payment-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 24px;
}

.payment-info .info-icon {
  color: #888888;
}

.payment-info .info-text {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #888888;
  font-weight: 400;
}

/* Success Content */
.success-content {
  text-align: center;
  padding: 16px; /* Reduced padding */
}

.success-header {
  margin-bottom: 24px; /* Reduced margin */
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px; /* Reduced gap */
}

.success-icon {
  width: 60px; /* Reduced size */
  height: 60px;
  border: 3px solid #10B981; /* Thinner border */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(16, 185, 129, 0.1);
}

.success-icon::before {
  content: '✓';
  font-size: 28px; /* Reduced font size */
  color: #10B981;
  font-weight: bold;
}

.success-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px; /* Reduced font size */
  font-weight: 700;
  color: #FFFFFF;
  margin: 0;
}

.success-subtitle {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px; /* Reduced font size */
  color: #CCCCCC;
  margin: 0;
  font-weight: 400;
  line-height: 1.4; /* Tighter line height */
  max-width: 320px; /* Reduced max width */
}

/* Staking Summary */
.staking-summary {
  background: #1A1A1A;
  border: 1px solid #404040;
  border-radius: 12px; /* Smaller border radius */
  padding: 16px; /* Reduced padding */
  margin-bottom: 20px; /* Reduced margin */
  text-align: left;
}

.summary-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px; /* Reduced font size */
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 12px 0; /* Reduced margin */
  text-align: center;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #333333;
}

.summary-row:last-child {
  border-bottom: none;
}

.summary-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #CCCCCC;
  font-weight: 500;
}

.summary-value {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #FFFFFF;
  font-weight: 600;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .stake-modal {
    margin: 8px;
    padding: 16px; /* Reduced padding */
    max-height: 85vh; /* Reduced max height */
    max-width: 95%;
  }

  .stake-modal-title {
    font-size: 16px; /* Smaller title */
  }

  .qr-code {
    padding: 8px; /* Reduced padding */
  }

  .qr-canvas {
    width: 140px !important; /* Smaller QR code */
    height: 140px !important;
  }

  .success-content {
    padding: 12px; /* Reduced padding */
  }

  .success-header {
    margin-bottom: 16px; /* Reduced margin */
    gap: 8px; /* Reduced gap */
  }

  .success-icon {
    width: 50px; /* Smaller icon */
    height: 50px;
    border: 2px solid #10B981; /* Thinner border */
  }

  .success-icon::before {
    font-size: 20px; /* Smaller checkmark */
  }

  .success-title {
    font-size: 18px; /* Smaller title */
  }

  .success-subtitle {
    font-size: 12px; /* Smaller subtitle */
    max-width: 280px; /* Reduced max width */
  }

  .timer-display {
    font-size: 18px;
    padding: 10px 20px;
  }

  .wallet-address {
    font-size: 12px;
  }

  .staking-summary {
    padding: 12px; /* Reduced padding */
    margin-bottom: 16px; /* Reduced margin */
  }

  .summary-title {
    font-size: 14px; /* Smaller title */
    margin-bottom: 8px; /* Reduced margin */
  }

  .summary-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 2px; /* Reduced gap */
    padding: 6px 0; /* Reduced padding */
  }

  .summary-label,
  .summary-value {
    font-size: 11px; /* Smaller text */
  }
}
