/* Live Reserve Page Styles */

/* Override dashboard content width for Live Reserve */
.live-reserve-container .dashboard-content {
  max-width: 1200px; /* Extended width for Live Reserve */
}

/* Live Reserve Header - Match Daily Transactions spacing */
.live-reserve-header {
  margin-bottom: 16px; /* Reduced spacing to match Daily Transactions */
}

.live-reserve-header .section-title {
  font-size: 24px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0; /* Remove default margin */
  font-family: 'Montserrat', sans-serif;
}

/* Holdings Grid - Extended Width Layout */
.holdings-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 24px; /* Increased gap for better spacing */
  margin: 0 0 32px 0; /* Remove top margin to start immediately under header */
  max-width: 100%; /* Use full available width */
  width: 100%;
}

/* Holding Card Styles - Using Daily Transactions card style */
.holding-card {
  background: rgba(38, 38, 38, 0.8); /* Match Daily Transactions card background */
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px; /* Match Daily Transactions border radius */
  padding: 16px 20px; /* Match Daily Transactions padding */
  display: flex;
  align-items: center;
  gap: 16px; /* Match Daily Transactions gap */
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  min-height: 120px; /* Match Daily Transactions height */
  text-align: left; /* Keep left alignment for Live Reserve cards */
}

.holding-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  border-color: rgba(191, 65, 41, 0.3);
}

/* Holding Icon Container */
.holding-icon-container {
  flex-shrink: 0;
}

.holding-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

/* Holding Info */
.holding-info {
  flex: 1;
  min-width: 0;
}

.holding-title {
  font-family: 'Montserrat', sans-serif; /* Match Daily Transactions font */
  font-size: 12px;
  font-weight: 600;
  color: #999999;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0 0 8px 0; /* Match Daily Transactions margin */
}

.holding-value {
  font-family: 'Montserrat', sans-serif; /* Match Daily Transactions font */
  font-size: 24px; /* Match Daily Transactions font size */
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 6px 0;
  display: flex;
  align-items: baseline;
  gap: 6px;
}

.holding-unit {
  font-size: 14px;
  font-weight: 500;
  color: #CCCCCC;
}

.holding-change {
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.holding-change.positive {
  color: #4ade80;
}

.holding-change.negative {
  color: #f87171;
}

.holding-change.neutral {
  color: #CCCCCC;
}

.change-icon {
  width: 12px;
  height: 12px;
}

/* Mobile Responsive - Match Daily Transactions style */
@media (max-width: 768px) {
  .holdings-grid {
    grid-template-columns: 1fr; /* Single column on mobile */
    gap: 16px;
  }

  .holding-card {
    padding: 24px 16px 20px 16px; /* Increased top padding to push title down */
    min-height: 120px; /* Increased height for better spacing */
    gap: 12px;
    width: 110%; /* Extended width to left and right */
    margin-left: -5%; /* Extend to the left */
    margin-right: -5%; /* Extend to the right */
  }

  .holding-icon {
    width: 40px;
    height: 40px;
  }

  .holding-value {
    font-size: 20px; /* Slightly smaller on mobile */
  }

  .holding-title {
    font-size: 12px; /* Better readable on mobile */
    margin-top: 8px; /* Push title down from top */
    margin-bottom: 12px; /* Add spacing below title */
  }
}

/* Additional Mobile Responsive - Remove duplicate styles */
@media (max-width: 768px) {
  /* Additional mobile optimizations for Live Reserve */
  .holding-icon-container {
    margin-top: 8px; /* Push icon down slightly */
  }

  .holding-info {
    margin-top: 4px; /* Push content down */
  }

  .holding-value {
    font-size: 20px;
  }
}

/* Tablet Responsive */
@media (min-width: 769px) and (max-width: 1024px) {
  .holdings-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 14px;
  }
}