/* Contact Us Container */
.contact-us-container {
  width: 100%;
  padding: 0;
}

/* Page Header - Reuse from treasury pages */
.contact-us-container .page-header {
  text-align: center;
  margin-bottom: 60px;
  padding: 0 40px;
}

.contact-us-container .page-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 48px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.contact-us-container .title-with-span {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.contact-us-container .title-span {
  width: 120px;
  height: auto;
}

.contact-us-container .page-subtitle {
  font-family: 'Montserrat', sans-serif;
  font-size: 18px;
  color: #CCCCCC;
  margin: 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.5;
}

/* Contact Section */
.contact-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.contact-header {
  text-align: center;
  margin-bottom: 40px;
}

.contact-title {
  font-family: '<PERSON><PERSON>rat', sans-serif;
  font-size: 36px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 16px 0;
}

.contact-subtitle {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  color: #CCCCCC;
  margin: 0;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.5;
}

/* Stay Safe Notice */
.stay-safe-notice {
  display: flex;
  align-items: center;
  gap: 16px;
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 12px;
  padding: 16px 20px;
  margin-bottom: 40px;
}

.notice-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.notice-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.notice-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #FFC107;
}

.notice-text {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #CCCCCC;
}

/* Contact Options */
.contact-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 60px;
}

.contact-option {
  background: #262626;
  border-radius: 16px;
  padding: 24px;
}

.option-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 20px 0;
}

.contact-method {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}

.contact-method:last-child {
  margin-bottom: 0;
}

.method-icon {
  font-size: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.method-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.method-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #FFFFFF;
}

.method-value {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #CCCCCC;
}

/* Contact Form Section */
.contact-form-section {
  background: #262626;
  border-radius: 16px;
  padding: 32px;
}

.form-section-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 8px 0;
}

.form-section-subtitle {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  color: #CCCCCC;
  margin: 0 0 32px 0;
}

/* Contact Form */
.contact-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #FFFFFF;
}

.form-input,
.form-textarea {
  background: #1A1A1A;
  border: 1px solid #404040;
  border-radius: 8px;
  padding: 14px 16px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #FFFFFF;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #E85A4F;
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: #666666;
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.submit-btn {
  background: #E85A4F;
  border: none;
  border-radius: 8px;
  padding: 16px 32px;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
  cursor: pointer;
  transition: background-color 0.3s ease;
  align-self: flex-start;
}

.submit-btn:hover {
  background: #D14A3F;
}

/* Success Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.success-modal {
  background: #262626;
  border-radius: 16px;
  padding: 40px;
  text-align: center;
  max-width: 400px;
  width: 90%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.success-icon {
  width: 64px;
  height: 64px;
  background: #10B981;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkmark {
  font-size: 32px;
  color: #FFFFFF;
  font-weight: bold;
}

.success-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0;
}

.success-message {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  color: #CCCCCC;
  margin: 0;
  line-height: 1.5;
}

.modal-btn {
  background: #E85A4F;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #FFFFFF;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.modal-btn:hover {
  background: #D14A3F;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .contact-us-container .page-header {
    padding: 0 20px;
    margin-bottom: 40px;
  }

  .contact-us-container .page-title {
    font-size: 32px;
  }

  .contact-us-container .title-with-span {
    flex-direction: column;
    gap: 12px;
  }

  .contact-us-container .title-span {
    width: 80px;
  }

  .contact-us-container .page-subtitle {
    font-size: 16px;
  }

  .contact-section {
    padding: 0 20px;
  }

  .contact-title {
    font-size: 28px;
  }

  .contact-subtitle {
    font-size: 14px;
  }

  .stay-safe-notice {
    flex-direction: column;
    text-align: center;
    gap: 12px;
    padding: 16px;
  }

  .contact-options {
    grid-template-columns: 1fr;
    gap: 16px;
    margin-bottom: 40px;
  }

  .contact-option {
    padding: 20px;
  }

  .option-title {
    font-size: 18px;
  }

  .contact-form-section {
    padding: 24px 20px;
  }

  .form-section-title {
    font-size: 20px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .success-modal {
    padding: 32px 24px;
    margin: 20px;
  }

  .success-icon {
    width: 56px;
    height: 56px;
  }

  .checkmark {
    font-size: 28px;
  }

  .success-title {
    font-size: 20px;
  }

  .success-message {
    font-size: 14px;
  }
}
