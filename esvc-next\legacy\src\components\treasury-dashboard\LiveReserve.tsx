import React, { useState } from 'react';
import DashboardLayout from '../layouts/DashboardLayout';
import SideNav from '../sidenavs/SideNav';
import '../../styles/components/treasury-dashboard/LiveReserve.css';

///Import icons
import spanImage from '../../assets/span.png';
import trendUpIcon from '../../assets/trend-up.png';
import trendDownIcon from '../../assets/trend-down.png';

// Import crypto asset icons
import bitcoinSymbol from '../../assets/bitcoin_symbol.png';
import solanaIcon from '../../assets/solana_icon.jpeg.png';
import usdcIcon from '../../assets/usdc.png';
import esvcToken from '../../assets/esvc-token.png';

interface LiveReserveProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const LiveReserve: React.FC<LiveReserveProps> = () => {
  const [activeTab, setActiveTab] = useState('live-reserve');



  const holdingsCards = [
    {
      title: 'BTC HOLDINGS',
      value: '$91,000',
      unit: 'BTC',
      change: '+4.8% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon,
      iconImage: bitcoinSymbol,
      iconClass: 'btc'
    },
    {
      title: 'SOLANA HOLDINGS',
      value: '$124,000',
      unit: 'SOL',
      change: '+4.8% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon,
      iconImage: solanaIcon,
      iconClass: 'sol'
    },
    {
      title: 'USDC HOLDINGS',
      value: '$51,500',
      unit: 'USDC',
      change: '-0.9% Today',
      changeType: 'negative',
      changeIcon: trendDownIcon,
      iconImage: usdcIcon,
      iconClass: 'usdc'
    },
    {
      title: 'ESVC RESERVES',
      value: '$51,500',
      unit: 'ESVC',
      change: '-0.9% Today',
      changeType: 'negative',
      changeIcon: trendDownIcon,
      iconImage: esvcToken,
      iconClass: 'esvc'
    }
  ];

  const totalValue = {
    title: 'TOTAL VALUE OF ALL HOLDINGS',
    value: '$318,000.00',
    change: '+4.8% Today',
    changeType: 'positive',
    changeIcon: trendUpIcon
  };



  return (
    <DashboardLayout className="live-reserve-container">
      <div className="live-reserve-content">
        {/* Page Title */}
        <div className="page-header">
          <h1 className="page-title">
            <div className="title-with-span">
              Treasury
              <img src={spanImage} alt="Decorative span" className="title-span" />
            </div>
            Dashboard
          </h1>
          <p className="page-subtitle">
            Live insights into how funds are held, ROI is distributed, and startups are supported.
            Empowering you to stake with trust.
          </p>
        </div>

        {/* Dashboard Layout */}
        <div className="dashboard-layout">
          {/* Dashboard Content Wrapper */}
          <div className="dashboard-content-wrapper">
            {/* Sidebar */}
            <SideNav
              activeTab={activeTab}
              onTabChange={setActiveTab}
            />

            {/* Dashboard Content */}
            <div className="dashboard-content">
            <div className="live-reserve-header">
              <h2 className="section-title">Live Reserve</h2>
            </div>
            
            <div className="holdings-grid">
              {holdingsCards.map((card, index) => (
                <div key={index} className="holding-card">
                  <div className="holding-icon-container">
                    <img src={card.iconImage} alt={card.title} className={`holding-icon ${card.iconClass}`} />
                  </div>
                  <div className="holding-info">
                    <h3 className="holding-title">{card.title}</h3>
                    <div className="holding-value">
                      {card.value}
                      <span className="holding-unit">{card.unit}</span>
                    </div>
                    <div className={`holding-change ${card.changeType}`}>
                      <img src={card.changeIcon} alt="Change indicator" className="change-icon" />
                      {card.change}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Total Value Card */}
            <div className="total-value-card">
              <h3 className="total-title">{totalValue.title}</h3>
              <div className="total-value">{totalValue.value}</div>
              <div className={`total-change ${totalValue.changeType}`}>
                <img src={totalValue.changeIcon} alt="Change indicator" className="change-icon" />
                {totalValue.change}
              </div>
            </div>
          </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default LiveReserve;
