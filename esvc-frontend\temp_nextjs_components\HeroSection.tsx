import Image from "next/image";

export default function HeroSection() {
  return (
    <section className="relative w-full">
      {/* Background blur effects */}
      <div className="absolute w-[239px] h-[239px] top-0 lg:right-0 bg-[#d19049] rounded-[119.5px] blur-[100px] opacity-30"></div>
      <div className="absolute w-[239px] h-[239px] top-[232px] lg:left-[22px] bg-[#cc6754] rounded-[119.5px] blur-[100px] opacity-30"></div>
      
      <section className="flex flex-col items-center gap-8 w-full max-w-4xl mx-auto mb-20 py-5 px-5 lg:px-0">
        <div className="flex flex-col items-center gap-4 w-full text-center relative">
          <div className="inline-flex items-center justify-center gap-2.5 px-4 rounded-full"></div>
          <div className="border border-neutral-700 rounded-full p-3 bg-gradient-to-r from-[#BF4129] to-[#D19049] bg-clip-text text-transparent">
            <h3>Earn 20% annual returns</h3>
          </div>
          
          <h1 className="font-montserrat font-bold md:text-[47px] text-[33px]">
            <span className="text-[#cc6754]">Grow</span>
            <span className="text-neutral-100"> Your Wealth. </span>
            <span className="text-[#d19049]">Get</span>
            <span className="text-neutral-100"> Funded. <br/> Stake and Earn .</span>
          </h1>
          
          <p className="max-w-[627px] font-montserrat font-normal text-neutral-300 text-base leading-6">
            Stake your ESVC tokens and earn daily ROI. <br/>
            Unlock exclusive opportunities to pitch your startup ideas for funding/get funded trade capital. <br/>
            Earn from our Bitcoin/Crypto Treasury
          </p>
          
          {/* Decorative elements */}
          <Image
            className="top-[220px] w-[200px] h-[13px] absolute md:top-[210px] md:w-[300px] right-1 md:right-1 md:-translate-x-1/2"
            alt="Decorative line"
            src="/c.animaapp.com/mc62dpc6QTKBF1/img/vector-1.svg"
            width={300}
            height={13}
          />
          <Image
            className="absolute w-44 h-[50px] top-[179px] left-[20px] md:w-44 md:h-[75px] md:top-[150px] md:left-[220px]"
            alt="Vector graphic circle"
            src="/c.animaapp.com/mc62dpc6QTKBF1/img/vector.svg"
            width={176}
            height={75}
          />
        </div>
        
        <button className="whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 text-primary-foreground shadow h-14 bg-[#bf4129] hover:bg-[#a83a25] rounded-full px-4 py-2.5 flex items-center justify-center gap-3">
          <span className="font-montserrat font-semibold text-neutral-50 text-lg">Start Staking Now</span>
          <Image
            className="w-6 h-6"
            alt="Rocket launch"
            src="/c.animaapp.com/mc62dpc6QTKBF1/img/rocket-launch-24dp-00000-fill0-wght300-grad0-opsz24-1.svg"
            width={24}
            height={24}
          />
        </button>
      </section>
      
      {/* Decorative coin images */}
      <Image
        className="absolute w-[106px] top-[401px] right-[-10px] md:w-[296px] md:h-[296px] md:top-[187px] md:right-[5px] object-cover"
        alt="Decorative coin image gold"
        src="/c.animaapp.com/mc62dpc6QTKBF1/img/whatsapp-image-2025-06-13-at-6-11-51-pm--2--1.png"
        width={296}
        height={296}
      />
      <Image
        className="w-[106px] h-[106px] top-[401px] left-0 md:w-[370px] md:h-[370px] md:top-[179px] md:left-0 absolute object-cover"
        alt="Decorative coin image red big"
        src="/c.animaapp.com/mc62dpc6QTKBF1/img/whatsapp-image-2025-06-13-at-6-11-51-pm--1--2.png"
        width={370}
        height={370}
      />
      <Image
        className="w-[78px] h-[78px] top-[492px] right-[20px] md:w-[156px] md:h-[156px] md:top-[352px] md:right-[256px] absolute object-cover"
        alt="Decorative coin image red small right"
        src="/c.animaapp.com/mc62dpc6QTKBF1/img/whatsapp-image-2025-06-13-at-6-11-51-pm--1--2.png"
        width={156}
        height={156}
      />
      <Image
        className="absolute w-[94px] h-[94px] top-[492px] left-[10px] md:w-[188px] md:h-[188px] md:top-[352px] md:left-[276px] object-cover"
        alt="Decorative coin image"
        src="/c.animaapp.com/mc62dpc6QTKBF1/img/whatsapp-image-2025-06-13-at-6-11-51-pm-1.png"
        width={188}
        height={188}
      />
    </section>
  );
}
