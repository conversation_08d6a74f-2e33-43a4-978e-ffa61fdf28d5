import React, { useState } from 'react';
import '../../styles/components/user-dashboard/StakeESVC.css';
import UserDashboardLayout from './UserDashboardLayout';
import UserSideNav from './UserSideNav';
import StakeESVCModal from '../modals/StakeESVCModal';

// Import icons
import cardCoinIcon from '../../assets/card-coin.png';
import esvcToken from '../../assets/esvc-token.png';
import usdcIcon from '../../assets/usdc.png';

const StakeESVC: React.FC = () => {
  const [activeTab, setActiveTab] = useState('stake-esvc');
  const [showBalances, setShowBalances] = useState(true);
  const [hideBalances, setHideBalances] = useState(false);
  const [showStakeModal, setShowStakeModal] = useState(false);

  // Toggle balance visibility
  const toggleBalances = () => {
    setShowBalances(!showBalances);
    setHideBalances(!hideBalances);
  };

  const handleStakeNow = () => {
    setShowStakeModal(true);
  };

  const handleStakeModalClose = () => {
    setShowStakeModal(false);
  };

  return (
    <UserDashboardLayout className="stake-esvc-container">
      <div className="stake-esvc-content">
        {/* User Greeting Header */}
        <div className="user-header">
          <div className="user-greeting">
            <h1 className="greeting-text">Hi, Oluwatosín 👋</h1>
            <p className="greeting-subtitle">Here is your staking overview</p>
          </div>
          
          <div className="header-controls">
            <button className="stake-esvc-btn" onClick={handleStakeNow}>
              <img src={cardCoinIcon} alt="Stake" className="btn-icon" />
              Stake ESVC
            </button>

            <div className="balance-toggle">
              <span className="toggle-label">Show balances</span>
              <label className="toggle-switch">
                <input
                  type="checkbox"
                  checked={showBalances}
                  onChange={toggleBalances}
                />
                <span className="toggle-slider"></span>
              </label>
              <span className="toggle-label">Hide balances</span>
            </div>
          </div>
        </div>

        <div className="dashboard-layout">
          {/* Dashboard Content Wrapper */}
          <div className="dashboard-content-wrapper">
            {/* Sidebar */}
            <UserSideNav
              activeTab={activeTab}
              onTabChange={setActiveTab}
            />

            {/* Main Content */}
            <div className="stake-main-content">
              {/* Staking Overview */}
              <div className="staking-overview">
                <h2 className="section-title">Staking Overview</h2>
                
                {/* Stats Cards */}
                <div className="stats-grid">
                  <div className="stat-card">
                    <div className="stat-icon">
                      <img src={esvcToken} alt="ESVC" />
                    </div>
                    <div className="stat-content">
                      <div className="stat-label">Total Staked</div>
                      <div className="stat-value">{showBalances ? '10,000 ESVC' : '••••••'}</div>
                      <div className="stat-subtext">{showBalances ? '$15,000.00' : '••••••'}</div>
                    </div>
                  </div>

                  <div className="stat-card">
                    <div className="stat-icon">
                      <img src={usdcIcon} alt="USDC" />
                    </div>
                    <div className="stat-content">
                      <div className="stat-label">Rewards Earned</div>
                      <div className="stat-value">{showBalances ? '1,250 USDC' : '••••••'}</div>
                      <div className="stat-subtext">{showBalances ? '+8.3% APY' : '••••••'}</div>
                    </div>
                  </div>

                  <div className="stat-card">
                    <div className="stat-icon">
                      <div className="duration-icon">⏱️</div>
                    </div>
                    <div className="stat-content">
                      <div className="stat-label">Average Duration</div>
                      <div className="stat-value">6 Months</div>
                      <div className="stat-subtext">Across all stakes</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Staking Options */}
              <div className="staking-options">
                <h3 className="options-title">Start Staking</h3>
                <p className="options-subtitle">Choose your staking duration and earn rewards</p>

                <div className="staking-cards">
                  <div className="staking-card">
                    <div className="card-header">
                      <h4 className="card-title">6 Months</h4>
                      <div className="apy-badge">8.5% APY</div>
                    </div>
                    <div className="card-content">
                      <div className="benefit-list">
                        <div className="benefit-item">✓ Quarterly rewards distribution</div>
                        <div className="benefit-item">✓ Early withdrawal available</div>
                        <div className="benefit-item">✓ Pitch eligibility after $500</div>
                      </div>
                      <button className="stake-option-btn" onClick={handleStakeNow}>
                        Stake Now
                      </button>
                    </div>
                  </div>

                  <div className="staking-card featured">
                    <div className="featured-badge">Most Popular</div>
                    <div className="card-header">
                      <h4 className="card-title">12 Months</h4>
                      <div className="apy-badge">12% APY</div>
                    </div>
                    <div className="card-content">
                      <div className="benefit-list">
                        <div className="benefit-item">✓ Monthly rewards distribution</div>
                        <div className="benefit-item">✓ Higher APY rewards</div>
                        <div className="benefit-item">✓ Priority pitch review</div>
                        <div className="benefit-item">✓ Exclusive events access</div>
                      </div>
                      <button className="stake-option-btn primary" onClick={handleStakeNow}>
                        Stake Now
                      </button>
                    </div>
                  </div>

                  <div className="staking-card">
                    <div className="card-header">
                      <h4 className="card-title">24 Months</h4>
                      <div className="apy-badge">15% APY</div>
                    </div>
                    <div className="card-content">
                      <div className="benefit-list">
                        <div className="benefit-item">✓ Weekly rewards distribution</div>
                        <div className="benefit-item">✓ Maximum APY rewards</div>
                        <div className="benefit-item">✓ VIP pitch sessions</div>
                        <div className="benefit-item">✓ Direct founder access</div>
                      </div>
                      <button className="stake-option-btn" onClick={handleStakeNow}>
                        Stake Now
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* How It Works */}
              <div className="how-it-works">
                <h3 className="section-title">How Staking Works</h3>
                <div className="steps-grid">
                  <div className="step-item">
                    <div className="step-number">1</div>
                    <div className="step-content">
                      <h4 className="step-title">Choose Duration</h4>
                      <p className="step-description">Select your preferred staking period from 6, 12, or 24 months</p>
                    </div>
                  </div>
                  <div className="step-item">
                    <div className="step-number">2</div>
                    <div className="step-content">
                      <h4 className="step-title">Stake ESVC</h4>
                      <p className="step-description">Deposit your ESVC tokens to start earning rewards immediately</p>
                    </div>
                  </div>
                  <div className="step-item">
                    <div className="step-number">3</div>
                    <div className="step-content">
                      <h4 className="step-title">Earn Rewards</h4>
                      <p className="step-description">Receive USDC rewards based on your staking duration and amount</p>
                    </div>
                  </div>
                  <div className="step-item">
                    <div className="step-number">4</div>
                    <div className="step-content">
                      <h4 className="step-title">Get Pitch Access</h4>
                      <p className="step-description">Stake $500+ to unlock startup pitch submission privileges</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stake ESVC Modal */}
      <StakeESVCModal
        isOpen={showStakeModal}
        onClose={handleStakeModalClose}
      />
    </UserDashboardLayout>
  );
};

export default StakeESVC;
