import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import '../../styles/components/user-dashboard/sidenavs/UserSideNav.css';

import overviewIcon from '../../assets/overview.png';
import walletMoneyIcon from '../../assets/wallet-money.png';
import arrow2Icon from '../../assets/arrow-2.png';
import moneysIcon from '../../assets/moneys.png';
import percentageSquareIcon from '../../assets/percentage-square.png';

interface UserSideNavItem {
  id: string;
  label: string;
  icon: string;
  route: string;
}

interface UserSideNavProps {
  activeTab: string;
  onTabChange?: (tabId: string) => void;
}

const UserSideNav: React.FC<UserSideNavProps> = ({ activeTab, onTabChange }) => {
  const navigate = useNavigate();
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  const sidebarItems: UserSideNavItem[] = [
    { id: 'overview', label: 'Overview', icon: overviewIcon, route: '/user-dashboard' },
    { id: 'my-stake', label: 'My Stake', icon: walletMoneyIcon, route: '/user-dashboard/my-stake' },
    { id: 'transactions', label: 'Transactions', icon: arrow2Icon, route: '/user-dashboard/transactions' },
    { id: 'get-funding', label: 'Get Funding', icon: moneysIcon, route: '/user-dashboard/get-funding' },
    { id: 'security-settings', label: 'Security Settings', icon: percentageSquareIcon, route: '/user-dashboard/security-settings' }
  ];

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    if (isMobile) {
      const activeElement = document.querySelector('.user-sidenav-item.active') as HTMLElement;
      const sidebarNav = document.querySelector('.user-sidenav-list') as HTMLElement;
      
      if (activeElement && sidebarNav) {
        const activeRect = activeElement.getBoundingClientRect();
        const navRect = sidebarNav.getBoundingClientRect();
        const scrollLeft = activeElement.offsetLeft - (navRect.width / 2) + (activeRect.width / 2);
        
        sidebarNav.scrollTo({
          left: scrollLeft,
          behavior: 'smooth'
        });
      }
    }
  }, [activeTab, isMobile]);

  const handleSidebarClick = (item: UserSideNavItem) => {
    if (onTabChange) {
      onTabChange(item.id);
    }
    
    navigate(item.route);
  };

  return (
    <div className="user-sidenav-container">
      <nav className="user-sidenav-list">
        {sidebarItems.map((item) => (
          <button
            key={item.id}
            className={`user-sidenav-item ${activeTab === item.id ? 'active' : ''}`}
            onClick={() => handleSidebarClick(item)}
          >
            <img 
              src={item.icon} 
              alt={item.label}
              className="user-sidenav-icon"
            />
            <span className="user-sidenav-label">{item.label}</span>
          </button>
        ))}
      </nav>
    </div>
  );
};

export default UserSideNav;
