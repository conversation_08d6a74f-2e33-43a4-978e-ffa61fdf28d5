'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

const ForgotPassword: React.FC = () => {
  const router = useRouter();
  const [step, setStep] = useState<'forgot' | 'verify' | 'reset'>('forgot');
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState(['', '', '', '', '', '']);
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isCodeInvalid, setIsCodeInvalid] = useState(false);
  const [resendTimer, setResendTimer] = useState(119); // 1:59 in seconds

  // Timer countdown effect
  useEffect(() => {
    if (step === 'verify' && resendTimer > 0) {
      const timer = setTimeout(() => {
        setResendTimer(resendTimer - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [step, resendTimer]);

  const handleSendCode = () => {
    // Simulate sending verification code
    setStep('verify');
    setResendTimer(119); // Reset timer
  };

  const handleChangeEmailAddress = () => {
    setStep('forgot');
    setVerificationCode(['', '', '', '', '', '']);
    setIsCodeInvalid(false);
  };

  const handleResendCode = () => {
    // Simulate resending code
    setVerificationCode(['', '', '', '', '', '']);
    setIsCodeInvalid(false);
    setResendTimer(119);
  };

  const handleCodeChange = (index: number, value: string) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newCode = [...verificationCode];
      newCode[index] = value;
      setVerificationCode(newCode);
      setIsCodeInvalid(false);

      // Auto-focus next input
      if (value && index < 5) {
        const nextInput = document.getElementById(`code-${index + 1}`);
        nextInput?.focus();
      }
    }
  };

  const handleVerifyCode = () => {
    const code = verificationCode.join('');
    if (code.length === 6) {
      // Simulate code verification
      if (code === '123456') { // Mock valid code
        setStep('reset');
      } else {
        setIsCodeInvalid(true);
      }
    }
  };

  const handleResetPassword = () => {
    // Simulate password reset
    console.log('Password reset successfully');
    router.push('/login'); // Return to login
  };

  const isPasswordValid = (password: string) => {
    const checks = {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /\d/.test(password),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };
    return checks;
  };

  const passwordChecks = isPasswordValid(newPassword);
  const allPasswordChecksValid = Object.values(passwordChecks).every(Boolean);
  const passwordsMatch = newPassword === confirmPassword && confirmPassword.length > 0;

  if (step === 'forgot') {
    return (
      <div className="flex items-center justify-center bg-neutral-900 py-20">
        <div className="w-full max-w-md">
          <div className="bg-[#262626] rounded-3xl p-8 shadow-xl">
            <div className="text-center mb-8">
              <h1 className="text-white text-3xl font-bold mb-4 font-montserrat">Forgot Your Password?</h1>
              <p className="text-[#CCCCCC] text-lg font-medium font-montserrat">We'll send you a verification code to reset your password</p>
            </div>

            <div className="space-y-6">
              <div>
                <label htmlFor="email" className="block text-white text-sm font-medium mb-2 font-montserrat">Email Address</label>
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="w-full h-12 bg-neutral-800 border border-neutral-700 rounded-full px-5 text-white font-montserrat text-base transition-all duration-300 focus:outline-none focus:border-[#BF4129] focus:ring-1 focus:ring-[#BF4129] placeholder-neutral-500"
                />
              </div>

              <button
                className="w-full h-12 bg-[#BF4129] hover:bg-[#a83a25] border-none rounded-full text-white font-montserrat text-base font-semibold cursor-pointer transition-all duration-300 disabled:bg-neutral-600 disabled:cursor-not-allowed"
                onClick={handleSendCode}
                disabled={!email}
              >
                Send Verification Code
              </button>

              <div className="text-center">
                <button
                  className="bg-transparent border-none text-[#D19049] font-montserrat text-sm font-medium cursor-pointer transition-colors duration-300 hover:text-[#BF4129]"
                  onClick={() => router.push('/login')}
                >
                  ← Back to Login
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (step === 'verify') {
    return (
      <div className="flex items-center justify-center bg-neutral-900 py-20">
        <div className="w-full max-w-md">
          <div className="bg-[#262626] rounded-3xl p-8 shadow-xl">
            <div className="text-center mb-8">
              <h1 className="text-white text-3xl font-bold mb-4 font-montserrat">Verify Your Email</h1>
              <p className="text-[#CCCCCC] text-lg font-medium font-montserrat">Enter the 6-digit code sent to your email <strong>{email}</strong> to continue</p>
            </div>

            <div className="space-y-6">
              <div className="flex gap-3 justify-center">
                {verificationCode.map((digit, index) => (
                  <input
                    key={index}
                    id={`code-${index}`}
                    type="text"
                    value={digit}
                    onChange={(e) => handleCodeChange(index, e.target.value)}
                    className={`w-12 h-12 bg-neutral-800 border rounded-xl text-center text-white font-montserrat text-xl font-semibold transition-all duration-300 focus:outline-none focus:border-[#BF4129] focus:ring-1 focus:ring-[#BF4129] ${isCodeInvalid ? 'border-red-500 bg-red-900/20' : 'border-neutral-700'}`}
                    maxLength={1}
                  />
                ))}
              </div>

              {isCodeInvalid && (
                <p className="text-red-400 text-sm text-center font-montserrat">Invalid or expired code. Please check and try again.</p>
              )}

              <button
                className="w-full h-12 bg-[#BF4129] hover:bg-[#a83a25] border-none rounded-full text-white font-montserrat text-base font-semibold cursor-pointer transition-all duration-300 disabled:bg-neutral-600 disabled:cursor-not-allowed"
                onClick={handleVerifyCode}
                disabled={verificationCode.join('').length !== 6}
              >
                Verify Code
              </button>

              <div className="text-center space-y-2">
                <p className="text-neutral-400 text-sm font-montserrat">Check your spam folder if you don't see it.</p>
                <button
                  className="bg-transparent border-none text-[#BF4129] font-montserrat text-sm font-medium cursor-pointer underline disabled:text-neutral-500 disabled:cursor-not-allowed disabled:no-underline hover:text-[#a83a25] disabled:hover:text-neutral-500"
                  onClick={handleResendCode}
                  disabled={resendTimer > 0}
                >
                  {resendTimer > 0
                    ? `Resend Code in ${Math.floor(resendTimer / 60)}:${(resendTimer % 60).toString().padStart(2, '0')}`
                    : 'Resend Code'
                  }
                </button>
              </div>

              <div className="text-center">
                <button
                  className="bg-transparent border-none text-[#D19049] font-montserrat text-sm font-medium cursor-pointer transition-colors duration-300 hover:text-[#BF4129]"
                  onClick={handleChangeEmailAddress}
                >
                  ← Change Email Address
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center bg-neutral-900 py-20">
      <div className="w-full max-w-md">
        <div className="bg-[#262626] rounded-3xl p-8 shadow-xl">
          <div className="text-center mb-8">
            <h1 className="text-white text-3xl font-bold mb-4 font-montserrat">Set a New Password</h1>
            <p className="text-[#CCCCCC] text-lg font-medium font-montserrat">Create a strong password for your account</p>
          </div>

          <div className="space-y-6">
            <div>
              <label htmlFor="new-password" className="block text-white text-sm font-medium mb-2 font-montserrat">New Password</label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="new-password"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  placeholder="Create a strong password"
                  className="w-full h-12 bg-neutral-800 border border-neutral-700 rounded-full px-5 pr-12 text-white font-montserrat text-base transition-all duration-300 focus:outline-none focus:border-[#BF4129] focus:ring-1 focus:ring-[#BF4129] placeholder-neutral-500"
                />
                <button
                  type="button"
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-transparent border-none text-neutral-400 cursor-pointer hover:text-neutral-200"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  👁
                </button>
              </div>
            </div>

            <div className="space-y-2">
              <div className={`text-sm flex items-center gap-2 font-montserrat ${passwordChecks.length ? 'text-green-400' : 'text-neutral-400'}`}>
                <span className={`text-xs ${passwordChecks.length ? 'text-green-400' : 'text-neutral-600'}`}>
                  {passwordChecks.length ? '●' : '○'}
                </span>
                Be at least 8 characters long
              </div>
              <div className={`text-sm flex items-center gap-2 font-montserrat ${passwordChecks.uppercase ? 'text-green-400' : 'text-neutral-400'}`}>
                <span className={`text-xs ${passwordChecks.uppercase ? 'text-green-400' : 'text-neutral-600'}`}>
                  {passwordChecks.uppercase ? '●' : '○'}
                </span>
                Include at least one uppercase letter (A-Z)
              </div>
              <div className={`text-sm flex items-center gap-2 font-montserrat ${passwordChecks.lowercase ? 'text-green-400' : 'text-neutral-400'}`}>
                <span className={`text-xs ${passwordChecks.lowercase ? 'text-green-400' : 'text-neutral-600'}`}>
                  {passwordChecks.lowercase ? '●' : '○'}
                </span>
                Include at least one lowercase letter (a-z)
              </div>
              <div className={`text-sm flex items-center gap-2 font-montserrat ${passwordChecks.number ? 'text-green-400' : 'text-neutral-400'}`}>
                <span className={`text-xs ${passwordChecks.number ? 'text-green-400' : 'text-neutral-600'}`}>
                  {passwordChecks.number ? '●' : '○'}
                </span>
                Include at least one number (0-9)
              </div>
              <div className={`text-sm flex items-center gap-2 font-montserrat ${passwordChecks.special ? 'text-green-400' : 'text-neutral-400'}`}>
                <span className={`text-xs ${passwordChecks.special ? 'text-green-400' : 'text-neutral-600'}`}>
                  {passwordChecks.special ? '●' : '○'}
                </span>
                Include at least one special character (!@#$%^&*)
              </div>
            </div>

            <div>
              <label htmlFor="confirm-password" className="block text-white text-sm font-medium mb-2 font-montserrat">Re-Enter New Password</label>
              <div className="relative">
                <input
                  type={showConfirmPassword ? 'text' : 'password'}
                  id="confirm-password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="Confirm your password"
                  className="w-full h-12 bg-neutral-800 border border-neutral-700 rounded-full px-5 pr-12 text-white font-montserrat text-base transition-all duration-300 focus:outline-none focus:border-[#BF4129] focus:ring-1 focus:ring-[#BF4129] placeholder-neutral-500"
                />
                <button
                  type="button"
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-transparent border-none text-neutral-400 cursor-pointer hover:text-neutral-200"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  👁
                </button>
              </div>
            </div>

            <button
              className="w-full h-12 bg-[#BF4129] hover:bg-[#a83a25] border-none rounded-full text-white font-montserrat text-base font-semibold cursor-pointer transition-all duration-300 disabled:bg-neutral-600 disabled:cursor-not-allowed"
              onClick={handleResetPassword}
              disabled={!allPasswordChecksValid || !passwordsMatch}
            >
              Reset Password
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
