'use client';

import React, { useState } from 'react';

type TransactionType = 'all' | 'inflow' | 'outflow' | 'internal';

const DailyTransactions: React.FC = () => {
  const [selectedType, setSelectedType] = useState<TransactionType>('all');
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);

  const transactions = [
    {
      id: '1',
      type: 'inflow',
      description: 'Trading Bot Revenue',
      amount: '+$2,450.00',
      asset: 'USDC',
      time: '14:32:15',
      hash: '******************************************',
      status: 'confirmed'
    },
    {
      id: '2',
      type: 'outflow',
      description: 'Staking Rewards Distribution',
      amount: '-$8,920.00',
      asset: 'ESVC',
      time: '12:15:42',
      hash: '******************************************',
      status: 'confirmed'
    },
    {
      id: '3',
      type: 'inflow',
      description: 'New Staker Deposit',
      amount: '+$5,000.00',
      asset: 'ETH',
      time: '11:28:33',
      hash: '0x925a3b8D4742d35Cc6634C0532925a3b8D4C05329',
      status: 'confirmed'
    },
    {
      id: '4',
      type: 'outflow',
      description: 'Startup Funding - TechFlow',
      amount: '-$50,000.00',
      asset: 'USDC',
      time: '09:45:18',
      hash: '******************************************',
      status: 'confirmed'
    },
    {
      id: '5',
      type: 'internal',
      description: 'Asset Rebalancing',
      amount: '$25,000.00',
      asset: 'BTC',
      time: '08:12:07',
      hash: '******************************************',
      status: 'confirmed'
    },
    {
      id: '6',
      type: 'inflow',
      description: 'Investment Return',
      amount: '+$3,200.00',
      asset: 'USDC',
      time: '07:33:51',
      hash: '******************************************',
      status: 'pending'
    }
  ];

  const filteredTransactions = selectedType === 'all' 
    ? transactions 
    : transactions.filter(tx => tx.type === selectedType);

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'inflow': return 'text-green-400';
      case 'outflow': return 'text-red-400';
      case 'internal': return 'text-blue-400';
      default: return 'text-neutral-400';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'inflow': return '↗';
      case 'outflow': return '↙';
      case 'internal': return '↔';
      default: return '•';
    }
  };

  return (
    <div className="space-y-8">
      {/* Header with Filters */}
      <div className="bg-[#262626] rounded-2xl p-8">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-6 mb-6">
          <h2 className="text-white text-2xl font-bold font-montserrat">Daily Transactions</h2>
          
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Date Picker */}
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="h-10 bg-neutral-800 border border-neutral-700 rounded-lg px-3 text-white font-montserrat text-sm focus:outline-none focus:border-[#BF4129]"
            />
            
            {/* Transaction Type Filter */}
            <div className="flex bg-neutral-800 rounded-lg p-1">
              {[
                { id: 'all', label: 'All' },
                { id: 'inflow', label: 'Inflow' },
                { id: 'outflow', label: 'Outflow' },
                { id: 'internal', label: 'Internal' }
              ].map((type) => (
                <button
                  key={type.id}
                  onClick={() => setSelectedType(type.id as TransactionType)}
                  className={`px-4 py-2 rounded-md font-montserrat text-sm font-medium transition-all duration-300 ${
                    selectedType === type.id
                      ? 'bg-[#BF4129] text-white'
                      : 'text-neutral-300 hover:text-white'
                  }`}
                >
                  {type.label}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Daily Summary */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-neutral-800 rounded-xl p-4">
            <p className="text-neutral-400 text-sm font-montserrat mb-1">Total Transactions</p>
            <p className="text-white text-2xl font-bold font-montserrat">{filteredTransactions.length}</p>
          </div>
          
          <div className="bg-neutral-800 rounded-xl p-4">
            <p className="text-neutral-400 text-sm font-montserrat mb-1">Total Inflow</p>
            <p className="text-green-400 text-2xl font-bold font-montserrat">+$10,650</p>
          </div>
          
          <div className="bg-neutral-800 rounded-xl p-4">
            <p className="text-neutral-400 text-sm font-montserrat mb-1">Total Outflow</p>
            <p className="text-red-400 text-2xl font-bold font-montserrat">-$58,920</p>
          </div>
          
          <div className="bg-neutral-800 rounded-xl p-4">
            <p className="text-neutral-400 text-sm font-montserrat mb-1">Net Change</p>
            <p className="text-red-400 text-2xl font-bold font-montserrat">-$48,270</p>
          </div>
        </div>
      </div>

      {/* Transactions List */}
      <div className="bg-[#262626] rounded-2xl p-8">
        <h3 className="text-white text-xl font-semibold font-montserrat mb-6">Transaction History</h3>
        
        <div className="space-y-4">
          {filteredTransactions.map((transaction) => (
            <div key={transaction.id} className="bg-neutral-800 rounded-xl p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    transaction.type === 'inflow' ? 'bg-green-500/20' :
                    transaction.type === 'outflow' ? 'bg-red-500/20' : 'bg-blue-500/20'
                  }`}>
                    <span className={`text-lg ${getTypeColor(transaction.type)}`}>
                      {getTypeIcon(transaction.type)}
                    </span>
                  </div>
                  
                  <div>
                    <h4 className="text-white font-montserrat font-semibold">{transaction.description}</h4>
                    <div className="flex items-center gap-4 mt-1">
                      <p className="text-neutral-400 text-sm font-montserrat">{transaction.time}</p>
                      <span className="text-neutral-500">•</span>
                      <p className="text-neutral-400 text-sm font-montserrat">{transaction.asset}</p>
                      <span className="text-neutral-500">•</span>
                      <div className={`px-2 py-1 rounded-full text-xs font-montserrat ${
                        transaction.status === 'confirmed' 
                          ? 'bg-green-500/20 text-green-400' 
                          : 'bg-yellow-500/20 text-yellow-400'
                      }`}>
                        {transaction.status}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="text-right">
                  <p className={`text-xl font-bold font-montserrat ${getTypeColor(transaction.type)}`}>
                    {transaction.amount}
                  </p>
                  <button className="text-[#BF4129] hover:text-[#a83a25] text-sm font-montserrat font-medium transition-colors duration-300 mt-1">
                    View Details
                  </button>
                </div>
              </div>
              
              {/* Transaction Hash */}
              <div className="mt-4 pt-4 border-t border-neutral-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-neutral-400 text-sm font-montserrat">Hash:</span>
                    <span className="text-neutral-300 text-sm font-montserrat font-mono">
                      {transaction.hash.slice(0, 10)}...{transaction.hash.slice(-8)}
                    </span>
                  </div>
                  <button className="text-[#BF4129] hover:text-[#a83a25] text-sm font-montserrat font-medium transition-colors duration-300">
                    View on Explorer →
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {/* Load More */}
        <div className="text-center mt-8">
          <button className="h-12 bg-transparent border border-[#BF4129] rounded-full text-[#BF4129] hover:bg-[#BF4129] hover:text-white font-montserrat text-base font-semibold cursor-pointer transition-all duration-300 px-8">
            Load More Transactions
          </button>
        </div>
      </div>

      {/* Transaction Analytics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-[#262626] rounded-2xl p-6">
          <h3 className="text-white text-lg font-semibold font-montserrat mb-4">Transaction Volume by Hour</h3>
          <div className="space-y-3">
            {[
              { hour: '00:00-06:00', volume: '$12,450', percentage: 15 },
              { hour: '06:00-12:00', volume: '$45,230', percentage: 55 },
              { hour: '12:00-18:00', volume: '$28,670', percentage: 35 },
              { hour: '18:00-24:00', volume: '$8,920', percentage: 10 }
            ].map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-neutral-300 font-montserrat text-sm">{item.hour}</span>
                <div className="flex items-center gap-3">
                  <div className="w-24 bg-neutral-700 rounded-full h-2">
                    <div 
                      className="bg-[#BF4129] h-2 rounded-full" 
                      style={{ width: `${item.percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-white font-montserrat font-semibold text-sm w-16 text-right">
                    {item.volume}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="bg-[#262626] rounded-2xl p-6">
          <h3 className="text-white text-lg font-semibold font-montserrat mb-4">Top Transaction Categories</h3>
          <div className="space-y-4">
            {[
              { category: 'Staking Rewards', amount: '$8,920', color: 'bg-blue-500' },
              { category: 'Trading Revenue', amount: '$2,450', color: 'bg-green-500' },
              { category: 'Startup Funding', amount: '$50,000', color: 'bg-purple-500' },
              { category: 'New Deposits', amount: '$5,000', color: 'bg-yellow-500' }
            ].map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`w-3 h-3 rounded-full ${item.color}`}></div>
                  <span className="text-neutral-300 font-montserrat text-sm">{item.category}</span>
                </div>
                <span className="text-white font-montserrat font-semibold">{item.amount}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DailyTransactions;
