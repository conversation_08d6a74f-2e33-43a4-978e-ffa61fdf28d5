import React, { useState } from 'react';
import '../../styles/components/user-dashboard/ChangePassword.css';
import UserDashboardLayout from './UserDashboardLayout';
import UserSideNav from './UserSideNav';

// Import icons
import lockIcon from '../../assets/wallet-money.png';
import tickCircleIcon from '../../assets/tick-circle.png';

const ChangePassword: React.FC = () => {
  const [activeTab, setActiveTab] = useState('security-settings');
  const [showBalances, setShowBalances] = useState(true);
  const [hideBalances, setHideBalances] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [passwordRequirements, setPasswordRequirements] = useState({
    minLength: false,
    hasUppercase: false,
    hasLowercase: false,
    hasNumber: false,
    hasSpecialChar: false,
    noCommonWords: false
  });

  // Toggle balance visibility
  const toggleBalances = () => {
    setShowBalances(!showBalances);
    setHideBalances(!hideBalances);
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Validate password requirements for new password
    if (name === 'newPassword') {
      setPasswordRequirements({
        minLength: value.length >= 8,
        hasUppercase: /[A-Z]/.test(value),
        hasLowercase: /[a-z]/.test(value),
        hasNumber: /\d/.test(value),
        hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(value),
        noCommonWords: !/(password|123456|qwerty)/i.test(value)
      });
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate passwords match
    if (formData.newPassword !== formData.confirmPassword) {
      alert('New passwords do not match');
      return;
    }

    // Check all password requirements are met
    const allRequirementsMet = Object.values(passwordRequirements).every(req => req);
    if (!allRequirementsMet) {
      alert('Please ensure all password requirements are met');
      return;
    }

    // Simulate password change
    setShowSuccessModal(true);
  };

  const handleSuccessModalClose = () => {
    setShowSuccessModal(false);
    // Reset form
    setFormData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
    setPasswordRequirements({
      minLength: false,
      hasUppercase: false,
      hasLowercase: false,
      hasNumber: false,
      hasSpecialChar: false,
      noCommonWords: false
    });
  };

  const handleGoBack = () => {
    window.history.back();
  };

  return (
    <UserDashboardLayout className="change-password-container">
      <div className="change-password-content">
        {/* User Greeting Header */}
        <div className="user-header">
          <div className="user-greeting">
            <h1 className="greeting-text">Hi, Oluwatosín 👋</h1>
            <p className="greeting-subtitle">Here is your staking overview</p>
          </div>
          
          <div className="header-controls">
            <button className="stake-esvc-btn">
              <img src={lockIcon} alt="Stake" className="btn-icon" />
              Stake ESVC
            </button>
            
            <div className="balance-toggle">
              <span className="toggle-label">Hide balances</span>
              <label className="toggle-switch">
                <input 
                  type="checkbox" 
                  checked={hideBalances}
                  onChange={toggleBalances}
                />
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>
        </div>

        {/* Change Password Content */}
        <div className="change-password-main">
          <div className="password-header">
            <button className="back-btn" onClick={handleGoBack}>
              ← Change Password
            </button>
          </div>

          <div className="password-form-container">
            <form onSubmit={handleSubmit} className="password-form">
              <div className="form-group">
                <label className="form-label">Current Password</label>
                <input
                  type="password"
                  name="currentPassword"
                  value={formData.currentPassword}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="••••••••"
                  required
                />
              </div>

              <div className="form-group">
                <label className="form-label">New Password</label>
                <input
                  type="password"
                  name="newPassword"
                  value={formData.newPassword}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="••••••••"
                  required
                />
              </div>

              <div className="password-requirements">
                <p className="requirements-title">Password must contain:</p>
                <ul className="requirements-list">
                  <li className={passwordRequirements.minLength ? 'requirement-met' : 'requirement-unmet'}>
                    At least 8 characters
                  </li>
                  <li className={passwordRequirements.hasUppercase ? 'requirement-met' : 'requirement-unmet'}>
                    At least one uppercase letter
                  </li>
                  <li className={passwordRequirements.hasLowercase ? 'requirement-met' : 'requirement-unmet'}>
                    At least one lowercase letter
                  </li>
                  <li className={passwordRequirements.hasNumber ? 'requirement-met' : 'requirement-unmet'}>
                    At least one number
                  </li>
                  <li className={passwordRequirements.hasSpecialChar ? 'requirement-met' : 'requirement-unmet'}>
                    At least one special character (!@#$%^&*)
                  </li>
                  <li className={passwordRequirements.noCommonWords ? 'requirement-met' : 'requirement-unmet'}>
                    Cannot be a common password
                  </li>
                </ul>
              </div>

              <div className="form-group">
                <label className="form-label">Re-Enter New Password</label>
                <input
                  type="password"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="••••••••"
                  required
                />
              </div>

              <button type="submit" className="change-password-btn">
                Change Password
              </button>
            </form>
          </div>
        </div>
      </div>

      {/* Success Modal */}
      {showSuccessModal && (
        <div className="success-modal-overlay">
          <div className="success-modal">
            <div className="success-content">
              <img src={tickCircleIcon} alt="Success" className="success-icon" />
              <h2 className="success-title">Password Updated Successfully!</h2>
              <p className="success-message">
                Your new password has been saved.
                You'll need to log in again with your new password.
              </p>
              <button className="success-btn" onClick={handleSuccessModalClose}>
                Got it
              </button>
            </div>
          </div>
        </div>
      )}

      {/* User Side Navigation */}
      <UserSideNav
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />
    </UserDashboardLayout>
  );
};

export default ChangePassword;
