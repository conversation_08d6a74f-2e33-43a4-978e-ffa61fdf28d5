/* Stake ESVC Standalone Page Styles */
.stake-esvc-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  color: white;
}

.stake-esvc-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

/* Hero Section */
.hero-section {
  text-align: center;
  margin-bottom: 80px;
  padding: 60px 0;
}

.title-section {
  margin-bottom: 40px;
}

.hero-title {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 18px;
  color: #b0b0b0;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.stake-esvc-btn.primary {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.stake-esvc-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 53, 0.3);
}

.btn-icon {
  width: 20px;
  height: 20px;
}

/* Main Content */
.main-content {
  display: flex;
  flex-direction: column;
  gap: 80px;
}

/* Staking Options */
.staking-options {
  text-align: center;
}

.section-title {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 16px;
  color: white;
}

.section-subtitle {
  font-size: 16px;
  color: #b0b0b0;
  margin-bottom: 40px;
}

.staking-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 40px;
}

.staking-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  position: relative;
  transition: all 0.3s ease;
}

.staking-card:hover {
  transform: translateY(-4px);
  border-color: rgba(255, 107, 53, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.staking-card.featured {
  border-color: #ff6b35;
  background: rgba(255, 107, 53, 0.1);
}

.featured-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-title {
  font-size: 20px;
  font-weight: 600;
  color: white;
}

.apy-badge {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
}

.benefit-list {
  margin-bottom: 24px;
}

.benefit-item {
  color: #b0b0b0;
  margin-bottom: 8px;
  font-size: 14px;
}

.stake-option-btn {
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stake-option-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.stake-option-btn.primary {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border: none;
}

.stake-option-btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

/* How It Works */
.how-it-works {
  text-align: center;
}

.steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 32px;
  margin-top: 40px;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.step-number {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 16px;
}

.step-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin-bottom: 8px;
}

.step-description {
  color: #b0b0b0;
  font-size: 14px;
  line-height: 1.5;
}

/* Bottom CTA */
.bottom-cta {
  text-align: center;
  padding: 60px 0;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.cta-title {
  font-size: 28px;
  font-weight: 700;
  color: white;
  margin-bottom: 12px;
}

.cta-subtitle {
  color: #b0b0b0;
  margin-bottom: 32px;
  font-size: 16px;
}

.stake-esvc-btn-bottom {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.stake-esvc-btn-bottom:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 53, 0.3);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .stake-esvc-content {
    padding: 20px;
  }

  .hero-section {
    padding: 40px 0;
    margin-bottom: 60px;
  }

  .hero-title {
    font-size: 32px;
  }

  .hero-subtitle {
    font-size: 16px;
  }

  .main-content {
    gap: 60px;
  }

  .section-title {
    font-size: 24px;
  }

  .staking-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .steps-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .bottom-cta {
    padding: 40px 20px;
  }

  .cta-title {
    font-size: 24px;
  }
}
