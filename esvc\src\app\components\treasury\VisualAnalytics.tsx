'use client';

import React, { useState } from 'react';

const VisualAnalytics: React.FC = () => {
  const [selectedChart, setSelectedChart] = useState('treasury-growth');

  const chartData = {
    'treasury-growth': {
      title: 'Treasury Growth Over Time',
      data: [
        { month: 'Jan', value: 1800000 },
        { month: 'Feb', value: 1950000 },
        { month: 'Mar', value: 2100000 },
        { month: 'Apr', value: 2200000 },
        { month: 'May', value: 2350000 },
        { month: 'Jun', value: 2400000 }
      ]
    },
    'staking-distribution': {
      title: 'Staking Distribution by Tier',
      data: [
        { tier: 'Gold', percentage: 26.5, value: 225000 },
        { tier: 'Silver', percentage: 44.7, value: 380000 },
        { tier: 'Bronze', percentage: 28.8, value: 245000 }
      ]
    },
    'revenue-sources': {
      title: 'Revenue Sources Breakdown',
      data: [
        { source: 'Trading Bot', percentage: 45, value: 45230 },
        { source: 'Staking', percentage: 28, value: 28450 },
        { source: 'Investments', percentage: 19, value: 18920 },
        { source: 'Platform Fees', percentage: 8, value: 8400 }
      ]
    }
  };

  const currentChart = chartData[selectedChart as keyof typeof chartData];

  return (
    <div className="space-y-8">
      {/* Chart Selection */}
      <div className="bg-[#262626] rounded-2xl p-8">
        <h2 className="text-white text-2xl font-bold font-montserrat mb-6">Visual Analytics Dashboard</h2>
        
        <div className="flex flex-wrap gap-4 mb-8">
          {[
            { id: 'treasury-growth', label: 'Treasury Growth' },
            { id: 'staking-distribution', label: 'Staking Distribution' },
            { id: 'revenue-sources', label: 'Revenue Sources' }
          ].map((chart) => (
            <button
              key={chart.id}
              onClick={() => setSelectedChart(chart.id)}
              className={`px-6 py-3 rounded-lg font-montserrat text-sm font-medium transition-all duration-300 ${
                selectedChart === chart.id
                  ? 'bg-[#BF4129] text-white'
                  : 'bg-neutral-800 text-neutral-300 hover:text-white hover:bg-neutral-700'
              }`}
            >
              {chart.label}
            </button>
          ))}
        </div>

        {/* Chart Display Area */}
        <div className="bg-neutral-800 rounded-xl p-8 min-h-[400px]">
          <h3 className="text-white text-xl font-semibold font-montserrat mb-6">{currentChart.title}</h3>
          
          {selectedChart === 'treasury-growth' && (
            <div className="space-y-6">
              {/* Line Chart Simulation */}
              <div className="relative h-64 bg-neutral-900 rounded-lg p-4">
                <div className="absolute inset-4">
                  {/* Y-axis labels */}
                  <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-neutral-400 text-xs font-montserrat">
                    <span>$2.4M</span>
                    <span>$2.1M</span>
                    <span>$1.8M</span>
                  </div>
                  
                  {/* Chart area */}
                  <div className="ml-12 h-full relative">
                    {/* Grid lines */}
                    <div className="absolute inset-0 flex flex-col justify-between">
                      {[...Array(4)].map((_, i) => (
                        <div key={i} className="border-t border-neutral-700"></div>
                      ))}
                    </div>
                    
                    {/* Data points */}
                    <div className="absolute inset-0 flex items-end justify-between">
                      {currentChart.data.map((point, index) => (
                        <div key={index} className="flex flex-col items-center">
                          <div 
                            className="w-3 h-3 bg-[#BF4129] rounded-full mb-2"
                            style={{ 
                              marginBottom: `${((point.value - 1800000) / 600000) * 200}px` 
                            }}
                          ></div>
                          <span className="text-neutral-400 text-xs font-montserrat">{point.month}</span>
                        </div>
                      ))}
                    </div>
                    
                    {/* Trend line simulation */}
                    <svg className="absolute inset-0 w-full h-full">
                      <path
                        d="M 50 180 Q 150 160 250 140 Q 350 120 450 100"
                        stroke="#BF4129"
                        strokeWidth="2"
                        fill="none"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              
              {/* Growth Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-neutral-700 rounded-lg p-4 text-center">
                  <p className="text-neutral-400 text-sm font-montserrat mb-1">6-Month Growth</p>
                  <p className="text-green-400 text-xl font-bold font-montserrat">+33.3%</p>
                </div>
                <div className="bg-neutral-700 rounded-lg p-4 text-center">
                  <p className="text-neutral-400 text-sm font-montserrat mb-1">Average Monthly</p>
                  <p className="text-blue-400 text-xl font-bold font-montserrat">+5.6%</p>
                </div>
                <div className="bg-neutral-700 rounded-lg p-4 text-center">
                  <p className="text-neutral-400 text-sm font-montserrat mb-1">Projected Annual</p>
                  <p className="text-purple-400 text-xl font-bold font-montserrat">+67%</p>
                </div>
              </div>
            </div>
          )}

          {selectedChart === 'staking-distribution' && (
            <div className="space-y-6">
              {/* Pie Chart Simulation */}
              <div className="flex flex-col lg:flex-row items-center gap-8">
                <div className="relative w-64 h-64">
                  <svg className="w-full h-full transform -rotate-90">
                    {/* Gold Tier - 26.5% */}
                    <circle
                      cx="128"
                      cy="128"
                      r="100"
                      fill="none"
                      stroke="#EAB308"
                      strokeWidth="20"
                      strokeDasharray={`${26.5 * 6.28} ${(100 - 26.5) * 6.28}`}
                      strokeDashoffset="0"
                    />
                    {/* Silver Tier - 44.7% */}
                    <circle
                      cx="128"
                      cy="128"
                      r="100"
                      fill="none"
                      stroke="#9CA3AF"
                      strokeWidth="20"
                      strokeDasharray={`${44.7 * 6.28} ${(100 - 44.7) * 6.28}`}
                      strokeDashoffset={`-${26.5 * 6.28}`}
                    />
                    {/* Bronze Tier - 28.8% */}
                    <circle
                      cx="128"
                      cy="128"
                      r="100"
                      fill="none"
                      stroke="#D97706"
                      strokeWidth="20"
                      strokeDasharray={`${28.8 * 6.28} ${(100 - 28.8) * 6.28}`}
                      strokeDashoffset={`-${(26.5 + 44.7) * 6.28}`}
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                      <p className="text-white text-2xl font-bold font-montserrat">850K</p>
                      <p className="text-neutral-400 text-sm font-montserrat">Total ESVC</p>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  {currentChart.data.map((item, index) => (
                    <div key={index} className="flex items-center gap-4">
                      <div className={`w-4 h-4 rounded-full ${
                        item.tier === 'Gold' ? 'bg-yellow-500' :
                        item.tier === 'Silver' ? 'bg-gray-400' : 'bg-amber-600'
                      }`}></div>
                      <div className="flex-1">
                        <p className="text-white font-montserrat font-semibold">{item.tier} Tier</p>
                        <p className="text-neutral-400 text-sm font-montserrat">{item.value.toLocaleString()} ESVC</p>
                      </div>
                      <p className="text-white font-montserrat font-bold">{item.percentage}%</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {selectedChart === 'revenue-sources' && (
            <div className="space-y-6">
              {/* Bar Chart Simulation */}
              <div className="space-y-4">
                {currentChart.data.map((item, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-white font-montserrat font-semibold">{item.source}</span>
                      <span className="text-neutral-400 font-montserrat">${item.value.toLocaleString()}</span>
                    </div>
                    <div className="w-full bg-neutral-700 rounded-full h-4">
                      <div 
                        className={`h-4 rounded-full ${
                          index === 0 ? 'bg-[#BF4129]' :
                          index === 1 ? 'bg-blue-500' :
                          index === 2 ? 'bg-green-500' : 'bg-purple-500'
                        }`}
                        style={{ width: `${item.percentage}%` }}
                      ></div>
                    </div>
                    <div className="text-right">
                      <span className="text-neutral-400 text-sm font-montserrat">{item.percentage}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-[#262626] rounded-2xl p-6">
          <h3 className="text-neutral-400 text-sm font-montserrat mb-2">Treasury Efficiency</h3>
          <p className="text-white text-3xl font-bold font-montserrat">94.2%</p>
          <div className="w-full bg-neutral-700 rounded-full h-2 mt-3">
            <div className="bg-green-500 h-2 rounded-full" style={{ width: '94.2%' }}></div>
          </div>
        </div>
        
        <div className="bg-[#262626] rounded-2xl p-6">
          <h3 className="text-neutral-400 text-sm font-montserrat mb-2">Asset Utilization</h3>
          <p className="text-white text-3xl font-bold font-montserrat">87.5%</p>
          <div className="w-full bg-neutral-700 rounded-full h-2 mt-3">
            <div className="bg-blue-500 h-2 rounded-full" style={{ width: '87.5%' }}></div>
          </div>
        </div>
        
        <div className="bg-[#262626] rounded-2xl p-6">
          <h3 className="text-neutral-400 text-sm font-montserrat mb-2">Risk Score</h3>
          <p className="text-white text-3xl font-bold font-montserrat">Low</p>
          <div className="w-full bg-neutral-700 rounded-full h-2 mt-3">
            <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '25%' }}></div>
          </div>
        </div>
        
        <div className="bg-[#262626] rounded-2xl p-6">
          <h3 className="text-neutral-400 text-sm font-montserrat mb-2">Performance Score</h3>
          <p className="text-white text-3xl font-bold font-montserrat">A+</p>
          <div className="w-full bg-neutral-700 rounded-full h-2 mt-3">
            <div className="bg-green-500 h-2 rounded-full" style={{ width: '95%' }}></div>
          </div>
        </div>
      </div>

      {/* Trend Analysis */}
      <div className="bg-[#262626] rounded-2xl p-8">
        <h2 className="text-white text-2xl font-bold font-montserrat mb-6">Trend Analysis</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <h3 className="text-white text-lg font-semibold font-montserrat mb-4">Growth Trends</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-neutral-800 rounded-lg">
                <span className="text-neutral-300 font-montserrat">Treasury Value</span>
                <div className="flex items-center gap-2">
                  <span className="text-green-400 text-sm">↗</span>
                  <span className="text-green-400 font-montserrat font-semibold">+15.2%</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-neutral-800 rounded-lg">
                <span className="text-neutral-300 font-montserrat">Active Stakers</span>
                <div className="flex items-center gap-2">
                  <span className="text-green-400 text-sm">↗</span>
                  <span className="text-green-400 font-montserrat font-semibold">+7.8%</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-neutral-800 rounded-lg">
                <span className="text-neutral-300 font-montserrat">Revenue</span>
                <div className="flex items-center gap-2">
                  <span className="text-green-400 text-sm">↗</span>
                  <span className="text-green-400 font-montserrat font-semibold">+12.4%</span>
                </div>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-white text-lg font-semibold font-montserrat mb-4">Performance Indicators</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-neutral-800 rounded-lg">
                <span className="text-neutral-300 font-montserrat">ROI Distribution</span>
                <div className="flex items-center gap-2">
                  <span className="text-blue-400 text-sm">→</span>
                  <span className="text-blue-400 font-montserrat font-semibold">Stable</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-neutral-800 rounded-lg">
                <span className="text-neutral-300 font-montserrat">Liquidity Ratio</span>
                <div className="flex items-center gap-2">
                  <span className="text-green-400 text-sm">↗</span>
                  <span className="text-green-400 font-montserrat font-semibold">Healthy</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-neutral-800 rounded-lg">
                <span className="text-neutral-300 font-montserrat">Risk Level</span>
                <div className="flex items-center gap-2">
                  <span className="text-yellow-400 text-sm">→</span>
                  <span className="text-yellow-400 font-montserrat font-semibold">Low</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Export Options */}
      <div className="bg-[#262626] rounded-2xl p-8">
        <h2 className="text-white text-2xl font-bold font-montserrat mb-6">Export & Reports</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <button className="h-12 bg-[#BF4129] hover:bg-[#a83a25] border-none rounded-lg text-white font-montserrat text-base font-semibold cursor-pointer transition-all duration-300">
            Download PDF Report
          </button>
          
          <button className="h-12 bg-transparent border border-[#BF4129] rounded-lg text-[#BF4129] hover:bg-[#BF4129] hover:text-white font-montserrat text-base font-semibold cursor-pointer transition-all duration-300">
            Export CSV Data
          </button>
          
          <button className="h-12 bg-transparent border border-neutral-600 rounded-lg text-neutral-300 hover:bg-neutral-700 hover:text-white font-montserrat text-base font-semibold cursor-pointer transition-all duration-300">
            Share Analytics
          </button>
        </div>
      </div>
    </div>
  );
};

export default VisualAnalytics;
