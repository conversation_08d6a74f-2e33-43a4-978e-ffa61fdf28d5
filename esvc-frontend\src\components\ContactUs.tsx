import React, { useState } from 'react';
import DashboardLayout from './DashboardLayout';
import '../styles/components/ContactUs.css';

// Import icons
import spanImage from '../assets/span.png';

const ContactUs: React.FC = () => {
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [formData, setFormData] = useState({
    fullName: '',
    emailAddress: '',
    subject: '',
    message: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Show success modal
    setShowSuccessModal(true);
  };

  const handleCloseModal = () => {
    setShowSuccessModal(false);
    // Reset form
    setFormData({
      fullName: '',
      emailAddress: '',
      subject: '',
      message: ''
    });
  };

  return (
    <DashboardLayout className="contact-us-container">
      <div className="contact-us-content">
        {/* Page Title */}
        <div className="page-header">
          <h1 className="page-title">
            <div className="title-with-span">
              Contact
              <img src={spanImage} alt="Decorative span" className="title-span" />
            </div>
            Us
          </h1>
          <p className="page-subtitle">
            Have questions about staking, startup pitching, or our platform?
            Reach out to our support team. We're happy to assist you.
          </p>
        </div>

        {/* Contact Us Section */}
        <div className="contact-section">
          <div className="contact-header">
            <h2 className="contact-title">We're Here to Help</h2>
            <p className="contact-subtitle">
              Have questions about staking, startup pitching, or our platform?
              Reach out to our support team.
              We're happy to assist you.
            </p>
          </div>

          {/* Stay Safe Notice */}
          <div className="stay-safe-notice">
            <div className="notice-icon">🛡️</div>
            <div className="notice-content">
              <span className="notice-title">Stay Safe — Protect Your Wallet</span>
              <span className="notice-text">All messages are handled securely. We'll never ask for your wallet backup passwords.</span>
            </div>
          </div>

          {/* Contact Options */}
          <div className="contact-options">
            {/* General Inquiries */}
            <div className="contact-option">
              <h3 className="option-title">General Inquiries</h3>
              <div className="contact-method">
                <div className="method-icon">📧</div>
                <div className="method-details">
                  <span className="method-label">Email</span>
                  <span className="method-value"><EMAIL></span>
                </div>
              </div>
              <div className="contact-method">
                <div className="method-icon">📞</div>
                <div className="method-details">
                  <span className="method-label">Response Time</span>
                  <span className="method-value">Usually within 24 hours</span>
                </div>
              </div>
            </div>

            {/* Startup Pitch Support */}
            <div className="contact-option">
              <h3 className="option-title">Startup Pitch Support</h3>
              <div className="contact-method">
                <div className="method-icon">📧</div>
                <div className="method-details">
                  <span className="method-label">Email</span>
                  <span className="method-value"><EMAIL></span>
                </div>
              </div>
              <div className="contact-method">
                <div className="method-icon">💡</div>
                <div className="method-details">
                  <span className="method-label">Questions about pitch eligibility, submissions, or feedback</span>
                </div>
              </div>
            </div>

            {/* Media & Partnerships */}
            <div className="contact-option">
              <h3 className="option-title">Media & Partnerships</h3>
              <div className="contact-method">
                <div className="method-icon">📧</div>
                <div className="method-details">
                  <span className="method-label">Email</span>
                  <span className="method-value"><EMAIL></span>
                </div>
              </div>
              <div className="contact-method">
                <div className="method-icon">🤝</div>
                <div className="method-details">
                  <span className="method-label">For partnerships, PR or collaborations</span>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="contact-form-section">
            <h3 className="form-section-title">Contact Form</h3>
            <p className="form-section-subtitle">Send us a quick message and we will respond promptly.</p>

            <form className="contact-form" onSubmit={handleSubmit}>
              <div className="form-row">
                <div className="form-group">
                  <label className="form-label">Full Name</label>
                  <input
                    type="text"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    className="form-input"
                    placeholder="Enter your full name..."
                    required
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Email Address</label>
                  <input
                    type="email"
                    name="emailAddress"
                    value={formData.emailAddress}
                    onChange={handleInputChange}
                    className="form-input"
                    placeholder="Enter your email address..."
                    required
                  />
                </div>
              </div>

              <div className="form-group">
                <label className="form-label">Subject</label>
                <input
                  type="text"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="What is this about..."
                  required
                />
              </div>

              <div className="form-group">
                <label className="form-label">Message</label>
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  className="form-textarea"
                  placeholder="Type your message here..."
                  rows={6}
                  required
                />
              </div>

              <button type="submit" className="submit-btn">
                Send Message
              </button>
            </form>
          </div>
        </div>
      </div>

      {/* Success Modal */}
      {showSuccessModal && (
        <div className="modal-overlay">
          <div className="success-modal">
            <div className="success-icon">
              <div className="checkmark">✓</div>
            </div>
            <h3 className="success-title">Thank you!</h3>
            <p className="success-message">
              Your message has been received. We'll get back to you shortly.
            </p>
            <button className="modal-btn" onClick={handleCloseModal}>
              Got it!
            </button>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
};

export default ContactUs;
