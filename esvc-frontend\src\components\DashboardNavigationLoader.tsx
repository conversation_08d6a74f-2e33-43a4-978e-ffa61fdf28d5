import React from 'react';
import '../styles/components/DashboardNavigationLoader.css';

interface DashboardNavigationLoaderProps {
  isVisible: boolean;
  targetDashboard: string;
}

const DashboardNavigationLoader: React.FC<DashboardNavigationLoaderProps> = ({ 
  isVisible, 
  targetDashboard 
}) => {
  if (!isVisible) return null;

  return (
    <div className="dashboard-loader-overlay">
      <div className="dashboard-loader-container">
        <div className="loader-spinner">
          <div className="spinner-ring"></div>
          <div className="spinner-ring"></div>
          <div className="spinner-ring"></div>
        </div>
        
        <div className="loader-content">
          <h3 className="loader-title">Please wait</h3>
          <p className="loader-message">
            Redirecting to {targetDashboard} Dashboard...
          </p>
        </div>
      </div>
    </div>
  );
};

export default DashboardNavigationLoader;
