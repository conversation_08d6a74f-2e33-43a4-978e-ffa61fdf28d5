
/* ISOLATED LANDING PAGE STYLES - MA<PERSON><PERSON>UM ISOLATION FROM OTHER COMPONENTS */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@100;200;300;400;500;600;700;800;900&display=swap');

/* Landing page container - COMPLETE ISOLATION */
.landing-page-container {
  /* CSS Variables */
  --landing-background: #171717;
  --landing-foreground: #ffffff;
  --landing-font-montserrat: 'Montserrat', sans-serif;
  --landing-color-neutral-50: #fafafa;
  --landing-color-neutral-100: #f5f5f5;
  --landing-color-neutral-300: #d4d4d4;
  --landing-color-neutral-500: #737373;
  --landing-color-neutral-600: #525252;
  --landing-color-neutral-700: #404040;
  --landing-color-neutral-800: #262626;
  --landing-color-neutral-900: #171717;
  --landing-color-primary: #bf4129;
  --landing-color-primary-hover: #a83a25;
  --landing-color-secondary: #d19049;
  --landing-color-accent: #cc6754;

  /* Base container styles with maximum isolation */
  background: var(--landing-background) !important;
  color: var(--landing-foreground) !important;
  font-family: var(--landing-font-montserrat) !important;
  margin: 0 !important;
  padding: 0 !important;
  min-height: 100vh !important;
  position: relative !important;
  box-sizing: border-box !important;
  display: block !important;
  width: 100% !important;
  isolation: isolate !important;
}

/* Universal reset for all children */
.landing-page-container *,
.landing-page-container *::before,
.landing-page-container *::after {
  box-sizing: border-box !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Font utility */
.landing-page-container .font-montserrat {
  font-family: var(--landing-font-montserrat) !important;
}

/* Background colors */
.landing-page-container .bg-primary {
  background-color: var(--landing-color-primary) !important;
}

.landing-page-container .bg-primary-hover:hover {
  background-color: var(--landing-color-primary-hover) !important;
}

.landing-page-container .bg-secondary {
  background-color: var(--landing-color-secondary) !important;
}

.landing-page-container .bg-accent {
  background-color: var(--landing-color-accent) !important;
}

.landing-page-container .bg-neutral-800 {
  background-color: var(--landing-color-neutral-800) !important;
}

.landing-page-container .bg-neutral-900 {
  background-color: var(--landing-color-neutral-900) !important;
}

/* Text colors */
.landing-page-container .text-primary {
  color: var(--landing-color-primary) !important;
}

.landing-page-container .text-secondary {
  color: var(--landing-color-secondary) !important;
}

.landing-page-container .text-accent {
  color: var(--landing-color-accent) !important;
}

.landing-page-container .text-neutral-50 {
  color: var(--landing-color-neutral-50) !important;
}

.landing-page-container .text-neutral-100 {
  color: var(--landing-color-neutral-100) !important;
}

.landing-page-container .text-neutral-300 {
  color: var(--landing-color-neutral-300) !important;
}

.landing-page-container .text-neutral-700 {
  color: var(--landing-color-neutral-700) !important;
}

/* Custom gradient backgrounds */
.landing-page-container .bg-gradient-primary {
  background: linear-gradient(38deg, rgba(209,144,73,1) 36%, rgba(191,65,41,1) 100%);
}

.landing-page-container .bg-gradient-blur {
  background: linear-gradient(55deg, rgba(209,144,73,1) 30%, rgba(191,65,41,1) 100%);
}

/* Blur effects */
.landing-page-container .blur-100 {
  filter: blur(100px);
}

.landing-page-container .blur-97 {
  filter: blur(97.91px);
}

.landing-page-container .blur-159 {
  filter: blur(159.41px);
}

.landing-page-container .blur-33 {
  filter: blur(33.05px);
}

/* Custom positioning and sizing */
.landing-page-container .rounded-999 {
  border-radius: 999px;
}

.landing-page-container .rounded-981 {
  border-radius: 981.72px;
}

/* Specific background colors */
.landing-page-container .bg-dark-brown {
  background-color: #1d1104;
}

.landing-page-container .bg-dark-section {
  background-color: #281705;
}

.landing-page-container .bg-mobile-menu {
  background-color: #262626;
}

.landing-page-container .bg-mobile-item {
  background-color: #3a3a3a;
}

/* Custom animations and transitions */
.landing-page-container .transition-transform-300 {
  transition: transform 300ms ease-in-out;
}

/* Utility classes for Tailwind-like styling */
.landing-page-container .relative { position: relative; }
.landing-page-container .absolute { position: absolute; }
.landing-page-container .w-full { width: 100%; }
.landing-page-container .h-full { height: 100%; }
.landing-page-container .flex { display: flex; }
.landing-page-container .flex-col { flex-direction: column; }
.landing-page-container .items-center { align-items: center; }
.landing-page-container .justify-center { justify-content: center; }
.landing-page-container .gap-2 { gap: 0.5rem; }
.landing-page-container .gap-3 { gap: 0.75rem; }
.landing-page-container .gap-4 { gap: 1rem; }
.landing-page-container .gap-8 { gap: 2rem; }
.landing-page-container .p-3 { padding: 0.75rem; }
.landing-page-container .px-4 { padding-left: 1rem; padding-right: 1rem; }
.landing-page-container .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.landing-page-container .py-5 { padding-top: 1.25rem; padding-bottom: 1.25rem; }
.landing-page-container .px-5 { padding-left: 1.25rem; padding-right: 1.25rem; }
.landing-page-container .mb-20 { margin-bottom: 5rem; }
.landing-page-container .mx-auto { margin-left: auto; margin-right: auto; }
.landing-page-container .text-center { text-align: center; }
.landing-page-container .text-sm { font-size: 0.875rem; }
.landing-page-container .text-base { font-size: 1rem; }
.landing-page-container .text-lg { font-size: 1.125rem; }
.landing-page-container .font-normal { font-weight: 400; }
.landing-page-container .font-medium { font-weight: 500; }
.landing-page-container .font-semibold { font-weight: 600; }
.landing-page-container .font-bold { font-weight: 700; }
.landing-page-container .leading-6 { line-height: 1.5rem; }
.landing-page-container .rounded-full { border-radius: 9999px; }
.landing-page-container .border { border-width: 1px; }
.landing-page-container .inline-flex { display: inline-flex; }
.landing-page-container .whitespace-nowrap { white-space: nowrap; }
.landing-page-container .transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; }
.landing-page-container .shadow { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1); }
.landing-page-container .object-cover { object-fit: cover; }

/* Responsive utilities */
@media (min-width: 768px) {
  .landing-page-container .md\\:text-\\[47px\\] { font-size: 47px; }
  .landing-page-container .md\\:w-\\[300px\\] { width: 300px; }
  .landing-page-container .md\\:h-\\[296px\\] { height: 296px; }
  .landing-page-container .md\\:top-\\[187px\\] { top: 187px; }
  .landing-page-container .md\\:right-\\[5px\\] { right: 5px; }
  .landing-page-container .md\\:w-\\[370px\\] { width: 370px; }
  .landing-page-container .md\\:h-\\[370px\\] { height: 370px; }
  .landing-page-container .md\\:top-\\[179px\\] { top: 179px; }
  .landing-page-container .md\\:left-0 { left: 0; }
  .landing-page-container .md\\:w-\\[156px\\] { width: 156px; }
  .landing-page-container .md\\:h-\\[156px\\] { height: 156px; }
  .landing-page-container .md\\:top-\\[352px\\] { top: 352px; }
  .landing-page-container .md\\:right-\\[256px\\] { right: 256px; }
  .landing-page-container .md\\:w-\\[188px\\] { width: 188px; }
  .landing-page-container .md\\:h-\\[188px\\] { height: 188px; }
  .landing-page-container .md\\:left-\\[276px\\] { left: 276px; }
}

@media (min-width: 1024px) {
  .landing-page-container .lg\\:px-0 { padding-left: 0; padding-right: 0; }
  .landing-page-container .lg\\:right-0 { right: 0; }
  .landing-page-container .lg\\:left-\\[22px\\] { left: 22px; }
}

/* Specific sizing classes */
.landing-page-container .w-\\[239px\\] { width: 239px; }
.landing-page-container .h-\\[239px\\] { height: 239px; }
.landing-page-container .w-\\[200px\\] { width: 200px; }
.landing-page-container .h-\\[13px\\] { height: 13px; }
.landing-page-container .w-44 { width: 11rem; }
.landing-page-container .h-\\[50px\\] { height: 50px; }
.landing-page-container .h-\\[75px\\] { height: 75px; }
.landing-page-container .w-\\[106px\\] { width: 106px; }
.landing-page-container .h-\\[106px\\] { height: 106px; }
.landing-page-container .w-\\[78px\\] { width: 78px; }
.landing-page-container .h-\\[78px\\] { height: 78px; }
.landing-page-container .w-\\[94px\\] { width: 94px; }
.landing-page-container .h-\\[94px\\] { height: 94px; }
.landing-page-container .w-6 { width: 1.5rem; }
.landing-page-container .h-6 { height: 1.5rem; }
.landing-page-container .h-14 { height: 3.5rem; }
.landing-page-container .max-w-4xl { max-width: 56rem; }
.landing-page-container .max-w-\\[627px\\] { max-width: 627px; }

/* Positioning classes */
.landing-page-container .top-0 { top: 0; }
.landing-page-container .top-\\[232px\\] { top: 232px; }
.landing-page-container .top-\\[220px\\] { top: 220px; }
.landing-page-container .top-\\[210px\\] { top: 210px; }
.landing-page-container .top-\\[179px\\] { top: 179px; }
.landing-page-container .top-\\[150px\\] { top: 150px; }
.landing-page-container .top-\\[401px\\] { top: 401px; }
.landing-page-container .top-\\[492px\\] { top: 492px; }
.landing-page-container .right-\\[-10px\\] { right: -10px; }
.landing-page-container .right-1 { right: 0.25rem; }
.landing-page-container .right-\\[20px\\] { right: 20px; }
.landing-page-container .left-0 { left: 0; }
.landing-page-container .left-\\[20px\\] { left: 20px; }
.landing-page-container .left-\\[220px\\] { left: 220px; }
.landing-page-container .left-\\[10px\\] { left: 10px; }

/* Text sizing */
.landing-page-container .text-\\[33px\\] { font-size: 33px; }

/* Opacity */
.landing-page-container .opacity-30 { opacity: 0.3; }

/* Border radius */
.landing-page-container .rounded-\\[119\\.5px\\] { border-radius: 119.5px; }

/* Background clip */
.landing-page-container .bg-clip-text {
  background-clip: text;
  -webkit-background-clip: text;
}

.landing-page-container .text-transparent {
  color: transparent;
}

/* Gradient text */
.landing-page-container .bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.landing-page-container .from-\\[\\#BF4129\\] {
  --tw-gradient-from: #BF4129;
  --tw-gradient-to: rgb(191 65 41 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.landing-page-container .to-\\[\\#D19049\\] {
  --tw-gradient-to: #D19049;
}

/* Focus styles */
.landing-page-container .focus-visible\\:outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.landing-page-container .focus-visible\\:ring-1:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

/* Disabled styles */
.landing-page-container .disabled\\:pointer-events-none:disabled {
  pointer-events: none;
}

.landing-page-container .disabled\\:opacity-50:disabled {
  opacity: 0.5;
}
