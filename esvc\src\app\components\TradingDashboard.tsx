'use client';

import React from 'react';

const TradingDashboard: React.FC = () => {
  return (
    <div className="w-full py-20">
      <div className="max-w-7xl mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-white text-5xl font-bold font-montserrat mb-4">ESVC Trade Bot Dashboard</h1>
          <p className="text-neutral-300 text-lg font-montserrat max-w-2xl mx-auto">
            Monitor your automated trading performance and manage your bot settings.
          </p>
        </div>

        {/* Dashboard Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          <div className="bg-[#262626] rounded-2xl p-6">
            <h3 className="text-neutral-400 text-sm font-montserrat mb-2">Total Balance</h3>
            <p className="text-white text-3xl font-bold font-montserrat">$1,250.00</p>
            <p className="text-green-400 text-sm font-montserrat mt-2">+12.5% this month</p>
          </div>
          
          <div className="bg-[#262626] rounded-2xl p-6">
            <h3 className="text-neutral-400 text-sm font-montserrat mb-2">Active Trades</h3>
            <p className="text-white text-3xl font-bold font-montserrat">3</p>
            <p className="text-blue-400 text-sm font-montserrat mt-2">2 profitable</p>
          </div>
          
          <div className="bg-[#262626] rounded-2xl p-6">
            <h3 className="text-neutral-400 text-sm font-montserrat mb-2">Today's P&L</h3>
            <p className="text-white text-3xl font-bold font-montserrat">+$45.20</p>
            <p className="text-green-400 text-sm font-montserrat mt-2">+3.6%</p>
          </div>
          
          <div className="bg-[#262626] rounded-2xl p-6">
            <h3 className="text-neutral-400 text-sm font-montserrat mb-2">Win Rate</h3>
            <p className="text-white text-3xl font-bold font-montserrat">78%</p>
            <p className="text-green-400 text-sm font-montserrat mt-2">Last 30 days</p>
          </div>
        </div>

        {/* Bot Status */}
        <div className="bg-[#262626] rounded-2xl p-8 mb-12">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-white text-2xl font-bold font-montserrat">Bot Status</h2>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-green-400 font-montserrat font-medium">Active</span>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="text-neutral-400 text-sm font-montserrat mb-2">Exchange</h4>
              <p className="text-white font-montserrat">Binance</p>
            </div>
            <div>
              <h4 className="text-neutral-400 text-sm font-montserrat mb-2">Trading Capital</h4>
              <p className="text-white font-montserrat">$1,000.00</p>
            </div>
            <div>
              <h4 className="text-neutral-400 text-sm font-montserrat mb-2">Strategy</h4>
              <p className="text-white font-montserrat">Conservative Growth</p>
            </div>
          </div>
        </div>

        {/* Recent Trades */}
        <div className="bg-[#262626] rounded-2xl p-8">
          <h2 className="text-white text-2xl font-bold font-montserrat mb-6">Recent Trades</h2>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-neutral-700">
                  <th className="text-neutral-400 text-sm font-montserrat font-medium text-left py-3">Pair</th>
                  <th className="text-neutral-400 text-sm font-montserrat font-medium text-left py-3">Type</th>
                  <th className="text-neutral-400 text-sm font-montserrat font-medium text-left py-3">Amount</th>
                  <th className="text-neutral-400 text-sm font-montserrat font-medium text-left py-3">P&L</th>
                  <th className="text-neutral-400 text-sm font-montserrat font-medium text-left py-3">Time</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b border-neutral-800">
                  <td className="text-white font-montserrat py-4">BTC/USDT</td>
                  <td className="text-green-400 font-montserrat py-4">BUY</td>
                  <td className="text-white font-montserrat py-4">$250.00</td>
                  <td className="text-green-400 font-montserrat py-4">+$12.50</td>
                  <td className="text-neutral-400 font-montserrat py-4">2 hours ago</td>
                </tr>
                <tr className="border-b border-neutral-800">
                  <td className="text-white font-montserrat py-4">ETH/USDT</td>
                  <td className="text-red-400 font-montserrat py-4">SELL</td>
                  <td className="text-white font-montserrat py-4">$180.00</td>
                  <td className="text-green-400 font-montserrat py-4">+$8.90</td>
                  <td className="text-neutral-400 font-montserrat py-4">4 hours ago</td>
                </tr>
                <tr className="border-b border-neutral-800">
                  <td className="text-white font-montserrat py-4">ADA/USDT</td>
                  <td className="text-green-400 font-montserrat py-4">BUY</td>
                  <td className="text-white font-montserrat py-4">$100.00</td>
                  <td className="text-red-400 font-montserrat py-4">-$2.30</td>
                  <td className="text-neutral-400 font-montserrat py-4">6 hours ago</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Bot Controls */}
        <div className="mt-12 flex flex-col sm:flex-row gap-4 justify-center">
          <button className="h-12 bg-red-600 hover:bg-red-700 border-none rounded-full text-white font-montserrat text-base font-semibold cursor-pointer transition-all duration-300 px-8">
            Stop Bot
          </button>
          <button className="h-12 bg-[#BF4129] hover:bg-[#a83a25] border-none rounded-full text-white font-montserrat text-base font-semibold cursor-pointer transition-all duration-300 px-8">
            Adjust Settings
          </button>
          <button className="h-12 bg-transparent border border-[#BF4129] rounded-full text-[#BF4129] hover:bg-[#BF4129] hover:text-white font-montserrat text-base font-semibold cursor-pointer transition-all duration-300 px-8">
            Download Report
          </button>
        </div>
      </div>
    </div>
  );
};

export default TradingDashboard;
