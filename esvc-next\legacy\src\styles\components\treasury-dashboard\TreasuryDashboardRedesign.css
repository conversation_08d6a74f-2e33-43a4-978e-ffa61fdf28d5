/* Redesigned Treasury Dashboard - Based on Figma Design */

/* Dashboard Layout */
.dashboard-layout {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 0 16px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Dashboard Content Wrapper */
.dashboard-content-wrapper {
  display: flex;
  gap: 16px;
}

/* Dashboard Content */
.dashboard-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Overview Header */
.overview-header {
  margin-bottom: 24px; /* Standardized margin for alignment */
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0;
}

/* Dashboard Cards Grid */
.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-top: 0; /* Ensure consistent alignment */
}

/* Dashboard Card - Compact Design */
.dashboard-card {
  background: rgba(38, 38, 38, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  min-height: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  border-color: rgba(191, 65, 41, 0.3);
}

/* Card Header */
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.card-title {
  font-size: 12px;
  font-weight: 600;
  color: #999999;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 4px;
  line-height: 1.2;
}

.title-icon {
  width: 14px;
  height: 14px;
  opacity: 0.7;
}

/* Card Content */
.card-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.card-value {
  font-size: 20px;
  font-weight: 700;
  color: #FFFFFF;
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.card-unit {
  font-size: 12px;
  font-weight: 500;
  color: #CCCCCC;
  margin-left: 4px;
}

.card-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.card-change.positive {
  color: #4ade80;
}

.card-change.negative {
  color: #f87171;
}

.card-change.neutral {
  color: #CCCCCC;
}

.change-icon {
  width: 12px;
  height: 12px;
}

/* Holdings Section */
.holdings-section {
  margin-top: 8px;
}

.holdings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.holdings-title {
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0;
}

/* Holdings Grid */
.holdings-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

/* Holding Card - Compact Design */
.holding-card {
  background: rgba(38, 38, 38, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.holding-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  border-color: rgba(191, 65, 41, 0.3);
}

/* Holding Icon - Smaller Size */
.holding-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #FFFFFF;
  flex-shrink: 0;
}

.holding-icon.btc {
  background: linear-gradient(135deg, #f7931a, #ff9500);
}

.holding-icon.sol {
  background: linear-gradient(135deg, #9945ff, #14f195);
}

.holding-icon.usdc {
  background: linear-gradient(135deg, #2775ca, #4dabf7);
}

.holding-icon.esvc {
  background: linear-gradient(135deg, #BF4129, #ff6b5b);
}

/* Holding Info */
.holding-info {
  flex: 1;
  min-width: 0;
}

.holding-label {
  font-size: 11px;
  font-weight: 600;
  color: #999999;
  text-transform: uppercase;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.holding-amount {
  font-size: 16px;
  font-weight: 700;
  color: #FFFFFF;
  margin-bottom: 2px;
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.currency {
  font-size: 11px;
  color: #CCCCCC;
  font-weight: 500;
}

.holding-change {
  font-size: 11px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.holding-change.positive {
  color: #4ade80;
}

.holding-change.negative {
  color: #f87171;
}

.holding-change.neutral {
  color: #CCCCCC;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .dashboard-cards {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .holdings-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .dashboard-card,
  .holding-card {
    padding: 12px;
  }

  .card-title {
    font-size: 11px;
  }

  .card-value {
    font-size: 18px;
  }

  .holding-icon {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }

  .holding-label {
    font-size: 10px;
  }

  .holding-amount {
    font-size: 14px;
  }
}

/* Tablet Responsive */
@media (min-width: 769px) and (max-width: 1024px) {
  .dashboard-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .holdings-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
