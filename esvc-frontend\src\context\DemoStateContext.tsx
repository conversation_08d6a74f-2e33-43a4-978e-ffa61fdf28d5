import React, { createContext, useContext, useState, useEffect } from 'react';
import type { ReactNode } from 'react';

// Types for our demo data
export interface UserData {
  id: string;
  name: string;
  email: string;
  totalStaked: number;
  currentValue: number;
  totalEarned: number;
  availableForWithdrawal: number;
  wallets: WalletData[];
  isLoggedIn: boolean;
}

export interface WalletData {
  id: string;
  name: string;
  address: string;
  balance: number;
  staked: number;
  earned: number;
  type: 'bitcoin' | 'ethereum' | 'solana' | 'usdc' | 'esvc';
}

export interface TreasuryData {
  totalReserve: number;
  totalStaked: number;
  totalProfit: number;
  dailyTransactions: TransactionData[];
  assets: AssetData[];
  stakingStats: StakingStats;
}

export interface TransactionData {
  id: string;
  type: 'stake' | 'withdraw' | 'roi' | 'funding';
  amount: number;
  currency: string;
  timestamp: Date;
  status: 'completed' | 'pending' | 'failed';
  description: string;
}

export interface AssetData {
  symbol: string;
  name: string;
  amount: number;
  value: number;
  change24h: number;
  icon: string;
}

export interface StakingStats {
  totalStakers: number;
  averageStake: number;
  totalRewards: number;
  apy: number;
}

export interface TradingData {
  totalBalance: number;
  pendingInTrade: number;
  capital: number;
  profit: number;
  loss: number;
  profitAnticipation: number;
  trades: TradeData[];
  performance: PerformanceData;
}

export interface TradeData {
  id: string;
  pair: string;
  type: 'buy' | 'sell';
  amount: number;
  price: number;
  timestamp: Date;
  status: 'completed' | 'pending' | 'cancelled';
  profit?: number;
}

export interface PerformanceData {
  totalTrades: number;
  winRate: number;
  totalProfit: number;
  totalLoss: number;
  bestTrade: number;
  worstTrade: number;
}

export type DashboardMode = 'user' | 'treasury' | 'trading';

interface DemoStateContextType {
  // Data
  userData: UserData;
  treasuryData: TreasuryData;
  tradingData: TradingData;
  
  // Dashboard mode
  dashboardMode: DashboardMode;
  setDashboardMode: (mode: DashboardMode) => void;
  
  // Actions
  updateUserData: (data: Partial<UserData>) => void;
  updateTreasuryData: (data: Partial<TreasuryData>) => void;
  updateTradingData: (data: Partial<TradingData>) => void;
  addTransaction: (transaction: Omit<TransactionData, 'id'>) => void;
  addTrade: (trade: Omit<TradeData, 'id'>) => void;
  
  // Auth
  login: (email: string, password: string) => boolean;
  logout: () => void;
  
  // Persistence
  saveToLocalStorage: () => void;
  loadFromLocalStorage: () => void;
  resetDemoData: () => void;
}

const DemoStateContext = createContext<DemoStateContextType | undefined>(undefined);

// Default demo data
const getDefaultUserData = (): UserData => ({
  id: 'user-001',
  name: 'Oluwatosin',
  email: '<EMAIL>',
  totalStaked: 788.50,
  currentValue: 13700,
  totalEarned: 700,
  availableForWithdrawal: 700,
  isLoggedIn: true,
  wallets: [
    {
      id: 'wallet-001',
      name: 'Main Wallet',
      address: '**********************************',
      balance: 2500,
      staked: 200,
      earned: 150,
      type: 'bitcoin'
    },
    {
      id: 'wallet-002',
      name: 'ETH Wallet',
      address: '******************************************',
      balance: 5000,
      staked: 300,
      earned: 200,
      type: 'ethereum'
    },
    {
      id: 'wallet-003',
      name: 'SOL Wallet',
      address: 'DRpbCBMxVnDK7maPM5tGv6MvB3v1sRMC7Yv1sRMC7Yv1',
      balance: 1500,
      staked: 150,
      earned: 100,
      type: 'solana'
    },
    {
      id: 'wallet-004',
      name: 'USDC Wallet',
      address: '******************************************',
      balance: 3000,
      staked: 88.50,
      earned: 150,
      type: 'usdc'
    },
    {
      id: 'wallet-005',
      name: 'ESVC Wallet',
      address: '******************************************',
      balance: 1700,
      staked: 50,
      earned: 100,
      type: 'esvc'
    }
  ]
});

const getDefaultTreasuryData = (): TreasuryData => ({
  totalReserve: 2500000,
  totalStaked: 1800000,
  totalProfit: 43700,
  dailyTransactions: [
    {
      id: 'tx-001',
      type: 'stake',
      amount: 1000,
      currency: 'ESVC',
      timestamp: new Date('2024-01-15T10:30:00'),
      status: 'completed',
      description: 'Staking reward distribution'
    },
    {
      id: 'tx-002',
      type: 'roi',
      amount: 250,
      currency: 'USD',
      timestamp: new Date('2024-01-15T14:20:00'),
      status: 'completed',
      description: 'Daily ROI payment'
    },
    {
      id: 'tx-003',
      type: 'funding',
      amount: 50000,
      currency: 'USD',
      timestamp: new Date('2024-01-15T16:45:00'),
      status: 'pending',
      description: 'Startup funding allocation'
    }
  ],
  assets: [
    { symbol: 'BTC', name: 'Bitcoin', amount: 45.5, value: 1950000, change24h: 2.5, icon: 'bitcoin_symbol.png.png' },
    { symbol: 'ETH', name: 'Ethereum', amount: 850, value: 340000, change24h: -1.2, icon: 'ethereum.png' },
    { symbol: 'SOL', name: 'Solana', amount: 2500, value: 125000, change24h: 4.8, icon: 'solana_icon.jpeg.png' },
    { symbol: 'USDC', name: 'USD Coin', amount: 75000, value: 75000, change24h: 0.1, icon: 'usdc.png' },
    { symbol: 'ESVC', name: 'ESVC Token', amount: 50000, value: 10000, change24h: 8.5, icon: 'esvc-token.png' }
  ],
  stakingStats: {
    totalStakers: 1250,
    averageStake: 1440,
    totalRewards: 125000,
    apy: 12.5
  }
});

const getDefaultTradingData = (): TradingData => ({
  totalBalance: 6500,
  pendingInTrade: 3700,
  capital: 2185,
  profit: 2185,
  loss: 185,
  profitAnticipation: 2185,
  trades: [
    {
      id: 'trade-001',
      pair: 'BTC/USD',
      type: 'buy',
      amount: 0.5,
      price: 42000,
      timestamp: new Date('2024-01-15T09:30:00'),
      status: 'completed',
      profit: 500
    },
    {
      id: 'trade-002',
      pair: 'ETH/USD',
      type: 'sell',
      amount: 2.0,
      price: 2500,
      timestamp: new Date('2024-01-15T11:15:00'),
      status: 'completed',
      profit: 200
    },
    {
      id: 'trade-003',
      pair: 'SOL/USD',
      type: 'buy',
      amount: 10,
      price: 50,
      timestamp: new Date('2024-01-15T14:20:00'),
      status: 'pending'
    }
  ],
  performance: {
    totalTrades: 45,
    winRate: 78.5,
    totalProfit: 2185,
    totalLoss: 185,
    bestTrade: 850,
    worstTrade: -120
  }
});

export const DemoStateProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [userData, setUserData] = useState<UserData>(getDefaultUserData());
  const [treasuryData, setTreasuryData] = useState<TreasuryData>(getDefaultTreasuryData());
  const [tradingData, setTradingData] = useState<TradingData>(getDefaultTradingData());
  const [dashboardMode, setDashboardMode] = useState<DashboardMode>('user');

  // Load data from localStorage on mount
  useEffect(() => {
    loadFromLocalStorage();
  }, []);

  // Save to localStorage whenever data changes
  useEffect(() => {
    saveToLocalStorage();
  }, [userData, treasuryData, tradingData, dashboardMode]);

  const updateUserData = (data: Partial<UserData>) => {
    setUserData(prev => ({ ...prev, ...data }));
  };

  const updateTreasuryData = (data: Partial<TreasuryData>) => {
    setTreasuryData(prev => ({ ...prev, ...data }));
  };

  const updateTradingData = (data: Partial<TradingData>) => {
    setTradingData(prev => ({ ...prev, ...data }));
  };

  const addTransaction = (transaction: Omit<TransactionData, 'id'>) => {
    const newTransaction: TransactionData = {
      ...transaction,
      id: `tx-${Date.now()}`
    };
    setTreasuryData(prev => ({
      ...prev,
      dailyTransactions: [newTransaction, ...prev.dailyTransactions]
    }));
  };

  const addTrade = (trade: Omit<TradeData, 'id'>) => {
    const newTrade: TradeData = {
      ...trade,
      id: `trade-${Date.now()}`
    };
    setTradingData(prev => ({
      ...prev,
      trades: [newTrade, ...prev.trades]
    }));
  };

  const login = (email: string, password: string): boolean => {
    // Simple demo login - accept any email/password
    if (email && password) {
      setUserData(prev => ({ ...prev, isLoggedIn: true, email }));
      return true;
    }
    return false;
  };

  const logout = () => {
    setUserData(prev => ({ ...prev, isLoggedIn: false }));
    setDashboardMode('user');
  };

  const saveToLocalStorage = () => {
    try {
      const demoState = {
        userData,
        treasuryData,
        tradingData,
        dashboardMode,
        lastUpdated: new Date().toISOString()
      };
      localStorage.setItem('esvc-demo-state', JSON.stringify(demoState));
    } catch (error) {
      console.error('Failed to save demo state to localStorage:', error);
    }
  };

  const loadFromLocalStorage = () => {
    try {
      const savedState = localStorage.getItem('esvc-demo-state');
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        if (parsedState.userData) setUserData(parsedState.userData);
        if (parsedState.treasuryData) setTreasuryData(parsedState.treasuryData);
        if (parsedState.tradingData) setTradingData(parsedState.tradingData);
        if (parsedState.dashboardMode) setDashboardMode(parsedState.dashboardMode);
      }
    } catch (error) {
      console.error('Failed to load demo state from localStorage:', error);
      resetDemoData();
    }
  };

  const resetDemoData = () => {
    setUserData(getDefaultUserData());
    setTreasuryData(getDefaultTreasuryData());
    setTradingData(getDefaultTradingData());
    setDashboardMode('user');
  };

  const value: DemoStateContextType = {
    userData,
    treasuryData,
    tradingData,
    dashboardMode,
    setDashboardMode,
    updateUserData,
    updateTreasuryData,
    updateTradingData,
    addTransaction,
    addTrade,
    login,
    logout,
    saveToLocalStorage,
    loadFromLocalStorage,
    resetDemoData
  };

  return (
    <DemoStateContext.Provider value={value}>
      {children}
    </DemoStateContext.Provider>
  );
};

export const useDemoState = (): DemoStateContextType => {
  const context = useContext(DemoStateContext);
  if (!context) {
    throw new Error('useDemoState must be used within a DemoStateProvider');
  }
  return context;
};
