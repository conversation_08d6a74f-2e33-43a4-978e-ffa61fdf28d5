import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import '../../styles/components/sidenavs/SideNav.css';

// Import icons
import overviewIcon from '../../assets/overview.png';
import walletMoneyIcon from '../../assets/wallet-money.png';
import arrow2Icon from '../../assets/arrow-2.png';
import cardCoinIcon from '../../assets/card-coin.png';
import moneysIcon from '../../assets/moneys.png';
import percentageSquareIcon from '../../assets/percentage-square.png';
import chartIcon from '../../assets/chart.png';

interface SideNavItem {
  id: string;
  label: string;
  icon: string;
  route: string;
}

interface SideNavProps {
  activeTab: string;
  onTabChange?: (tabId: string) => void;
}

const SideNav: React.FC<SideNavProps> = ({ activeTab, onTabChange }) => {
  const navigate = useNavigate();
  const [isMobile, setIsMobile] = useState(false);

  const sidebarItems: SideNavItem[] = [
    { id: 'overview', label: 'Overview', icon: overviewIcon, route: '/overview' },
    { id: 'live-reserve', label: 'Live Reserve', icon: walletMoneyIcon, route: '/live-reserve' },
    { id: 'daily-transactions', label: 'Daily Transactions', icon: arrow2Icon, route: '/daily-transactions' },
    { id: 'real-time-staking', label: 'Real-Time Staking', icon: cardCoinIcon, route: '/real-time-staking' },
    { id: 'startup-funding', label: 'Startup Funding', icon: moneysIcon, route: '/startup-funding' },
    { id: 'roi-distribution', label: 'ROI Distribution', icon: percentageSquareIcon, route: '/roi-distribution' },
    { id: 'visual-analytics', label: 'Visual Analytics', icon: chartIcon, route: '/visual-analytics' }
  ];

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Center active item on mobile
  useEffect(() => {
    if (isMobile) {
      const activeElement = document.querySelector('.sidenav-item.active') as HTMLElement;
      const sidebarNav = document.querySelector('.sidenav-list') as HTMLElement;
      
      if (activeElement && sidebarNav) {
        const activeRect = activeElement.getBoundingClientRect();
        const navRect = sidebarNav.getBoundingClientRect();
        const scrollLeft = activeElement.offsetLeft - (navRect.width / 2) + (activeRect.width / 2);
        
        sidebarNav.scrollTo({
          left: scrollLeft,
          behavior: 'smooth'
        });
      }
    }
  }, [activeTab, isMobile]);

  const handleSidebarClick = (item: SideNavItem) => {
    // Call the onTabChange callback if provided
    if (onTabChange) {
      onTabChange(item.id);
    }
    
    // Navigate to the route
    navigate(item.route);
  };

  return (
    <div className="sidenav-container">
      <nav className="sidenav-list">
        {sidebarItems.map((item) => (
          <button
            key={item.id}
            className={`sidenav-item ${activeTab === item.id ? 'active' : ''}`}
            onClick={() => handleSidebarClick(item)}
          >
            <img 
              src={item.icon} 
              alt={item.label}
              className="sidenav-icon"
            />
            <span className="sidenav-label">{item.label}</span>
          </button>
        ))}
      </nav>
    </div>
  );
};

export default SideNav;
