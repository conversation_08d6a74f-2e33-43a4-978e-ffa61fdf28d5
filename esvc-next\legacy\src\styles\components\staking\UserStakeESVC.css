/* Stake ESVC Container */
.stake-esvc-container {
  width: 100%;
  padding: 0;
}

/* User Header Background - Reuse from other dashboard pages */
.stake-esvc-container .user-header::before {
  content: '';
  position: absolute;
  top: -120px;
  left: -50vw;
  right: -50vw;
  height: 360px !important;
  background: #260D08;
  z-index: -1;
}

/* User Header */
.stake-esvc-container .user-header {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px 40px; /* Reduced top and bottom padding */
  margin-bottom: 0;
}

.user-greeting {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.greeting-text {
  font-family: 'Montserrat', sans-serif;
  font-size: 32px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0;
}

.greeting-subtitle {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  color: #CCCCCC;
  margin: 0;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 32px;
}

.stake-esvc-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #E85A4F;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #FFFFFF;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.stake-esvc-btn:hover {
  background: #D14A3F;
}

.btn-icon {
  width: 16px;
  height: 16px;
}

.balance-toggle {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toggle-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #CCCCCC;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #404040;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #E85A4F;
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

/* Dashboard Layout */
.stake-esvc-container .dashboard-layout {
  display: flex;
  gap: 32px;
  align-items: flex-start;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  margin-top: 10px;
}

/* Dashboard Content Wrapper */
.dashboard-content-wrapper {
  display: flex;
  gap: 40px;
  width: 100%;
}

.stake-main-content {
  flex: 1;
  max-width: calc(100% - 280px - 32px);
}

/* Staking Overview */
.staking-overview {
  margin-bottom: 40px;
}

.section-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 24px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.stat-card {
  background: #262626;
  border-radius: 16px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(232, 90, 79, 0.1);
  border-radius: 12px;
}

.stat-icon img {
  width: 32px;
  height: 32px;
}

.duration-icon {
  font-size: 24px;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #CCCCCC;
}

.stat-value {
  font-family: 'Montserrat', sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #FFFFFF;
}

.stat-subtext {
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  color: #10B981;
}

/* Staking Options */
.staking-options {
  margin-bottom: 40px;
}

.options-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 8px 0;
}

.options-subtitle {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  color: #CCCCCC;
  margin: 0 0 32px 0;
}

.staking-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.staking-card {
  background: #262626;
  border-radius: 16px;
  padding: 24px;
  position: relative;
  border: 2px solid transparent;
  transition: border-color 0.3s ease;
}

.staking-card.featured {
  border-color: #E85A4F;
}

.featured-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: #E85A4F;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  font-weight: 600;
  padding: 6px 16px;
  border-radius: 12px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0;
}

.apy-badge {
  background: rgba(16, 185, 129, 0.1);
  color: #10B981;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  padding: 6px 12px;
  border-radius: 8px;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.benefit-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.benefit-item {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #CCCCCC;
}

.stake-option-btn {
  background: #404040;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #FFFFFF;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.stake-option-btn:hover {
  background: #505050;
}

.stake-option-btn.primary {
  background: #E85A4F;
}

.stake-option-btn.primary:hover {
  background: #D14A3F;
}

/* How It Works */
.how-it-works {
  margin-bottom: 40px;
}

.steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.step-item {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.step-number {
  width: 32px;
  height: 32px;
  background: #E85A4F;
  color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.step-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0;
}

.step-description {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #CCCCCC;
  margin: 0;
  line-height: 1.4;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .stake-esvc-container .user-header {
    flex-direction: column;
    gap: 16px; /* Reduced gap */
    padding: 16px 20px 24px; /* Reduced padding */
    text-align: left; /* Align to left instead of center */
  }

  .header-controls {
    flex-direction: column;
    gap: 16px;
    width: 100%;
    align-items: flex-start; /* Align controls to left */
  }

  .stake-esvc-btn {
    width: auto;
    min-width: 100px;
    justify-content: center;
    padding: 10px 20px !important;
    font-size: 12px;
    margin: 0 0 16px 0 !important;
    align-self: flex-start; /* Align button to left */
  }

  .stake-esvc-container .dashboard-layout {
    flex-direction: column;
    gap: 20px;
    padding: 0 20px;
    margin-top: -60px !important; /* Reduced negative margin to shift tabs down */
  }

  .dashboard-content-wrapper {
    flex-direction: column;
    gap: 16px;
  }

  .dashboard-mode-tabs {
    display: block; /* Show tabs on mobile */
    margin-bottom: 24px; /* Add spacing below tabs */
    order: 0; /* Ensure tabs appear first */
  }

  .stake-esvc-container .user-sidenav-container {
    margin-top: 40px; /* Increased margin to push sidenav down */
    order: 1;
  }

  .stake-main-content {
    max-width: 100%;
    order: 2;
  }

  .greeting-text {
    font-size: 24px;
  }

  .greeting-subtitle {
    font-size: 14px;
  }

  .section-title {
    font-size: 20px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .stat-card {
    padding: 20px;
  }

  .options-title {
    font-size: 20px;
  }

  .options-subtitle {
    font-size: 14px;
  }

  .staking-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .staking-card {
    padding: 20px;
  }

  .steps-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .step-item {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .step-number {
    align-self: center;
  }
}
