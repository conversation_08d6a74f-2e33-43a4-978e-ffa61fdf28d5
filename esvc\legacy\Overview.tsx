import React, { useState } from 'react';
import '../styles/components/Overview.css';
import DashboardLayout from './DashboardLayout';
import SideNav from './SideNav';
import { useDemoState } from '../context/DemoStateContext';

// Import icons
import spanImage from '../assets/span.png';
import trendUpIcon from '../assets/trend-up.png';
import informationCircleIcon from '../assets/information-circle.png';

// Import crypto asset icons
// import bitcoinSymbol from '../assets/bitcoin_symbol.png';
// import solanaIcon from '../assets/solana_icon.jpeg.png';
// import usdcIcon from '../assets/usdc.png';
// import esvcToken from '../assets/esvc-token.png';


interface OverviewProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const Overview: React.FC<OverviewProps> = () => {
  const { treasuryData } = useDemoState();
  const [activeTab, setActiveTab] = useState('overview');





  const dashboardCards = [
    {
      title: 'TOTAL AMOUNT OF ESVC STAKED',
      value: treasuryData.totalStaked.toLocaleString(),
      unit: 'ESVC',
      change: '+2.4% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon
    },
    {
      title: 'FUNDS AVAILABLE FOR DAILY ISO PAYOUTS',
      value: '$609,185',
      change: '+7.8% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon
    },
    {
      title: 'FUNDS AVAILABLE TO FUND STARTUPS',
      value: '$43,700',
      change: '+4.8% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon,
      titleIcon: informationCircleIcon
    },
    {
      title: 'TOTAL NUMBER OF STAKERS',
      value: treasuryData.stakingStats.totalStakers.toLocaleString(),
      change: '+3.8% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon
    },
    {
      title: 'TOTAL ESVC SOLD TO DATE',
      value: '$29,400,200.00',
      change: '+2.4% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon
    },
    {
      title: 'TOTAL PROFIT GENERATED',
      value: `$${treasuryData.totalProfit.toLocaleString()}`,
      change: '+1.8% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon
    }
  ];



  return (
    <DashboardLayout className="overview-container">
      <div className="overview-content">
        {/* Page Title */}
        <div className="page-header">
          <h1 className="page-title">
            <div className="title-with-span">
              Treasury
              <img src={spanImage} alt="Decorative span" className="title-span" />
            </div>
            Dashboard
          </h1>
          <p className="page-subtitle">
            Live insights into how funds are held, ROI is distributed, and startups are supported.
            Empowering you to stake with trust.
          </p>
        </div>

        <div className="dashboard-layout">
          {/* Dashboard Content Wrapper */}
          <div className="dashboard-content-wrapper">
            {/* Sidebar */}
            <SideNav
              activeTab={activeTab}
              onTabChange={setActiveTab}
            />

            {/* Dashboard Content */}
            <div className="dashboard-content">
              <div className="overview-header">
                <h2 className="section-title">Overview</h2>
              </div>

              <div className="dashboard-cards">
                {dashboardCards.map((card, index) => (
                  <div key={index} className="dashboard-card">
                    <div className="card-header">
                      <h3 className="card-title">
                        {card.title}
                        {card.titleIcon && <img src={card.titleIcon} alt="Title icon" className="title-icon" />}
                      </h3>
                    </div>
                    <div className="card-content">
                      <div className="card-value">
                        {card.value}
                        {card.unit && <span className="card-unit">{card.unit}</span>}
                      </div>
                      <div className={`card-change ${card.changeType}`}>
                        <img src={card.changeIcon} alt="Change indicator" className="change-icon" />
                        {card.change}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Overview;
