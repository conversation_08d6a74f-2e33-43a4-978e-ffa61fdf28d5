/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cworkspace%5C%5C.typescript%5C%5Cesvc%5C%5Cesvc%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cworkspace%5C%5C.typescript%5C%5Cesvc%5C%5Cesvc%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5CFAQSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cworkspace%5C%5C.typescript%5C%5Cesvc%5C%5Cesvc%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cworkspace%5C%5C.typescript%5C%5Cesvc%5C%5Cesvc%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cworkspace%5C%5C.typescript%5C%5Cesvc%5C%5Cesvc%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5CTreasuryDashboard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cworkspace%5C%5C.typescript%5C%5Cesvc%5C%5Cesvc%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cworkspace%5C%5C.typescript%5C%5Cesvc%5C%5Cesvc%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5CFAQSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cworkspace%5C%5C.typescript%5C%5Cesvc%5C%5Cesvc%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cworkspace%5C%5C.typescript%5C%5Cesvc%5C%5Cesvc%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cworkspace%5C%5C.typescript%5C%5Cesvc%5C%5Cesvc%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5CTreasuryDashboard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(app-pages-browser)/./node_modules/next/dist/client/image-component.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/components/FAQSection.tsx */ \"(app-pages-browser)/./src/app/components/FAQSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/components/Footer.tsx */ \"(app-pages-browser)/./src/app/components/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/components/Header.tsx */ \"(app-pages-browser)/./src/app/components/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/components/TreasuryDashboard.tsx */ \"(app-pages-browser)/./src/app/components/TreasuryDashboard.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cworkspace%5C%5C.typescript%5C%5Cesvc%5C%5Cesvc%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cworkspace%5C%5C.typescript%5C%5Cesvc%5C%5Cesvc%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5CFAQSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cworkspace%5C%5C.typescript%5C%5Cesvc%5C%5Cesvc%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cworkspace%5C%5C.typescript%5C%5Cesvc%5C%5Cesvc%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cworkspace%5C%5C.typescript%5C%5Cesvc%5C%5Cesvc%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5CTreasuryDashboard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/components/TreasuryDashboard.tsx":
/*!**************************************************!*\
  !*** ./src/app/components/TreasuryDashboard.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TreasuryDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction TreasuryDashboard() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleStartStaking = ()=>{\n        // Navigate to staking page\n        router.push('/user-dashboard/my-stake');\n    };\n    const handleViewTreasuryDashboard = ()=>{\n        // Navigate to Treasury Dashboard page\n        router.push('/treasury-dashboard');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative w-full bg-gradient-to-br from-[#1a1a1a] via-[#1f1f1f] to-[#1a1a1a] py-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute w-[239px] h-[239px] top-0 right-0 lg:right-0 bg-[#d19049] rounded-full blur-[100px] opacity-30\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute w-[239px] h-[239px] top-[232px] left-[22px] lg:left-[22px] bg-[#cc6754] rounded-full blur-[100px] opacity-30\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 right-0 bottom-0 bg-gradient-radial from-[rgba(232,90,79,0.05)] via-transparent to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 right-0 bottom-0 bg-gradient-radial from-[rgba(212,175,55,0.05)] via-transparent to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container max-w-[1200px] mx-auto px-5 relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-15\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-5xl md:text-6xl font-bold text-white mb-4 font-montserrat\",\n                                children: \"Treasury Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"max-w-[600px] mx-auto text-lg text-neutral-300 leading-relaxed font-montserrat\",\n                                children: [\n                                    \"Live insights into how funds are held, ROI is distributed, and startups are supported.\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Empowering you to stake with trust.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:flex md:flex-col w-full gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:flex items-center gap-6 w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-xl border text-card-foreground shadow flex-1 bg-transparent border-neutral-700 mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-start gap-4 p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 w-full mt-[-1.00px]\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-neutral-300 text-sm font-montserrat\",\n                                                        children: \"Total Capital Invested\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                        lineNumber: 47,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 46,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"self-stretch font-semibold text-neutral-100 text-[28px] font-montserrat\",\n                                                    children: \"$266,500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 49,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            width: \"24\",\n                                                            height: \"24\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            strokeWidth: \"2\",\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            className: \"lucide lucide-trending-up w-4 h-4 text-[#7cca8d]\",\n                                                            \"aria-hidden\": \"true\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M16 7h6v6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                                    lineNumber: 52,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"m22 7-8.5 8.5-5-5L2 17\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                                    lineNumber: 53,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                            lineNumber: 51,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-normal text-xs font-montserrat text-[#7cca8d]\",\n                                                            children: \"+ 4.8% Today\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                            lineNumber: 55,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-xl border text-card-foreground shadow flex-1 bg-transparent border-neutral-700 mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-start gap-4 p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 w-full mt-[-1.00px]\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-neutral-300 text-sm font-montserrat\",\n                                                        children: \"Total Profit Generated\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                        lineNumber: 64,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"self-stretch font-semibold text-neutral-100 text-[28px] font-montserrat\",\n                                                    children: \"$43,700\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            width: \"24\",\n                                                            height: \"24\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            strokeWidth: \"2\",\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            className: \"lucide lucide-trending-up w-4 h-4 text-[#7cca8d]\",\n                                                            \"aria-hidden\": \"true\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M16 7h6v6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                                    lineNumber: 69,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"m22 7-8.5 8.5-5-5L2 17\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                                    lineNumber: 70,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                            lineNumber: 68,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-normal text-xs font-montserrat text-[#7cca8d]\",\n                                                            children: \"+ 4.8% Today\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                            lineNumber: 72,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-xl border text-card-foreground shadow flex-1 bg-transparent border-neutral-700 mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-start gap-4 p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 w-full mt-[-1.00px]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-neutral-300 text-sm font-montserrat\",\n                                                            children: \"Funds Available to Fund Startups\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                            lineNumber: 81,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            width: \"24\",\n                                                            height: \"24\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            strokeWidth: \"2\",\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            className: \"lucide lucide-info w-5 h-5 text-neutral-300\",\n                                                            \"aria-hidden\": \"true\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: \"12\",\n                                                                    cy: \"12\",\n                                                                    r: \"10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                                    lineNumber: 83,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M12 16v-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                                    lineNumber: 84,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M12 8h.01\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                                    lineNumber: 85,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                            lineNumber: 82,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"self-stretch font-semibold text-neutral-100 text-[28px] font-montserrat\",\n                                                    children: \"$2,185\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            width: \"24\",\n                                                            height: \"24\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            strokeWidth: \"2\",\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            className: \"lucide lucide-trending-up w-4 h-4 text-[#7cca8d]\",\n                                                            \"aria-hidden\": \"true\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M16 7h6v6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                                    lineNumber: 91,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"m22 7-8.5 8.5-5-5L2 17\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                                    lineNumber: 92,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                            lineNumber: 90,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-normal text-xs font-montserrat text-[#7cca8d]\",\n                                                            children: \"+ 4.8% Today\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                            lineNumber: 94,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-xl border text-card-foreground shadow w-full bg-transparent border-neutral-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 pt-0 md:flex md:items-start md:gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 flex flex-col items-start gap-3 p-4 rounded-2xl overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                            className: \"w-11 h-11 rounded-4xl\",\n                                                            alt: \"BTC HOLDINGS\",\n                                                            src: \"/c.animaapp.com/mc62dpc6QTKBF1/img/bitcoin-symbol-png.png\",\n                                                            width: 44,\n                                                            height: 44\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col items-start gap-3 flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"self-stretch font-medium text-neutral-300 text-sm font-montserrat\",\n                                                                    children: \"BTC HOLDINGS\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                                    lineNumber: 114,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"self-stretch font-semibold text-neutral-100 text-xl font-montserrat\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"$91,000 \"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                                            lineNumber: 116,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm\",\n                                                                            children: \"BTC\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                                            lineNumber: 117,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                                    lineNumber: 115,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                            lineNumber: 113,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            width: \"24\",\n                                                            height: \"24\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            strokeWidth: \"2\",\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            className: \"lucide lucide-trending-up w-4 h-4 text-[#7cca8d]\",\n                                                            \"aria-hidden\": \"true\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M16 7h6v6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                                    lineNumber: 123,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"m22 7-8.5 8.5-5-5L2 17\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                                    lineNumber: 124,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                            lineNumber: 122,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-normal text-xs font-montserrat text-[#7cca8d]\",\n                                                            children: \"+ 4.8% Today\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    className: \"self-stretch object-cover py-2 hidden md:block\",\n                                                    alt: \"Line\",\n                                                    src: \"/c.animaapp.com/mc62dpc6QTKBF1/img/line-1.svg\",\n                                                    width: 1,\n                                                    height: 100\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    \"data-orientation\": \"horizontal\",\n                                                    role: \"none\",\n                                                    className: \"shrink-0 h-[1px] w-full bg-neutral-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 flex flex-col items-start gap-3 p-4 rounded-2xl overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                            className: \"w-11 h-11 rounded-4xl\",\n                                                            alt: \"SOL HOLDINGS\",\n                                                            src: \"/c.animaapp.com/mc62dpc6QTKBF1/img/solana-icon-jpeg.png\",\n                                                            width: 44,\n                                                            height: 44\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col items-start gap-3 flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"self-stretch font-medium text-neutral-300 text-sm font-montserrat\",\n                                                                    children: \"SOL HOLDINGS\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                                    lineNumber: 152,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"self-stretch font-semibold text-neutral-100 text-xl font-montserrat\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"$175,500 \"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                                            lineNumber: 154,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm\",\n                                                                            children: \"SOL\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                                            lineNumber: 155,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                                    lineNumber: 153,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                            className: \"w-4 h-4\",\n                                                            alt: \"Trend icon\",\n                                                            src: \"/c.animaapp.com/mc62dpc6QTKBF1/img/trend-icon-1.svg\",\n                                                            width: 16,\n                                                            height: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-normal text-xs font-montserrat text-[#7cca8d]\",\n                                                            children: \"+ 4.8% Today\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    className: \"self-stretch object-cover py-2 hidden md:block\",\n                                                    alt: \"Line\",\n                                                    src: \"/c.animaapp.com/mc62dpc6QTKBF1/img/line-1.svg\",\n                                                    width: 1,\n                                                    height: 100\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    \"data-orientation\": \"horizontal\",\n                                                    role: \"none\",\n                                                    className: \"shrink-0 h-[1px] w-full bg-neutral-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 flex flex-col items-start gap-3 p-4 rounded-2xl overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                            className: \"w-11 h-11 rounded-4xl\",\n                                                            alt: \"USDC HOLDINGS\",\n                                                            src: \"/c.animaapp.com/mc62dpc6QTKBF1/img/image.png\",\n                                                            width: 44,\n                                                            height: 44\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col items-start gap-3 flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"self-stretch font-medium text-neutral-300 text-sm font-montserrat\",\n                                                                    children: \"USDC HOLDINGS\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"self-stretch font-semibold text-neutral-100 text-xl font-montserrat\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"$0 \"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                                            lineNumber: 195,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm\",\n                                                                            children: \"USDC\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                                            lineNumber: 196,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                                    lineNumber: 194,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                            className: \"w-4 h-4\",\n                                                            alt: \"Trend icon\",\n                                                            src: \"/c.animaapp.com/mc62dpc6QTKBF1/img/trend-icon-1.svg\",\n                                                            width: 16,\n                                                            height: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-normal text-xs font-montserrat text-[#7cca8d]\",\n                                                            children: \"+ 4.8% Today\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        className: \"absolute w-[39px] md:w-[78px] md:h-[52px] bottom-17 right-[-20px] md:bottom-[0px] md:right-[250px]\",\n                        alt: \"Element\",\n                        src: \"/c.animaapp.com/mc62dpc6QTKBF1/img/element-09.svg\",\n                        width: 78,\n                        height: 52\n                    }, void 0, false, {\n                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:flex md:items-center md:justify-center md:gap-6 mt-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"inline-flex items-center justify-center gap-2 whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 text-primary-foreground shadow mb-5 md:mb-0 h-14 w-full px-4 py-2.5 bg-[#bf4129] hover:bg-[#a83a25] rounded-[999px] text-lg font-semibold font-montserrat\",\n                                    children: [\n                                        \"Start Staking Now\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            width: \"24\",\n                                            height: \"24\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"2\",\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            className: \"lucide lucide-rocket w-6 h-6 ml-3\",\n                                            \"aria-hidden\": \"true\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"inline-flex items-center justify-center gap-2 whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-14 w-full px-4 py-2.5 rounded-[999px] border-neutral-700 text-neutral-100 text-lg font-semibold font-montserrat hover:border-neutral-500\",\n                                    children: [\n                                        \"View Full Breakdown\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            width: \"24\",\n                                            height: \"24\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"2\",\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            className: \"lucide lucide-arrow-right w-6 h-6 ml-3\",\n                                            \"aria-hidden\": \"true\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M5 12h14\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"m12 5 7 7-7 7\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n_s(TreasuryDashboard, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = TreasuryDashboard;\nvar _c;\n$RefreshReg$(_c, \"TreasuryDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/TreasuryDashboard.tsx\n"));

/***/ })

});