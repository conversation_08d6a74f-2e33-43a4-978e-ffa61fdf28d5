import React from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/components/TreasuryDashboard.css';

const TreasuryDashboard: React.FC = () => {
  const navigate = useNavigate();

  const handleStartStaking = () => {
    // Navigate to My Stake page in user dashboard
    navigate('/user-dashboard/my-stake');
  };

  const handleViewTreasuryDashboard = () => {
    // Navigate to Treasury Overview page
    navigate('/overview');
  };

  return (
    <section className="treasury-dashboard">
      <div className="container">
        <div className="dashboard-header">
          <h2 className="dashboard-title">Treasury Dashboard</h2>
          <p className="dashboard-subtitle">
            Live insights into how funds are held, ROI is distributed, and startups are supported.
            <br />
            Empowering you to stake with trust.
          </p>
        </div>

        {/* Top Stats */}
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-label">TOTAL CAPITAL INVESTED</div>
            <div className="stat-value">$266,500</div>
            <div className="stat-change positive">
              <span className="change-icon">📈</span>
              +4.8% Today
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-label">TOTAL PROFIT GENERATED</div>
            <div className="stat-value">$43,700</div>
            <div className="stat-change positive">
              <span className="change-icon">📈</span>
              +4.8% Today
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-label">
              FUNDS AVAILABLE TO FUND STARTUPS
              <span className="info-icon">ℹ️</span>
            </div>
            <div className="stat-value">$2,185</div>
            <div className="stat-change positive">
              <span className="change-icon">📈</span>
              +4.8% Today
            </div>
          </div>
        </div>

        {/* Holdings */}
        <div className="holdings-grid">
          <div className="holding-card">
            <div className="holding-icon btc">₿</div>
            <div className="holding-info">
              <div className="holding-label">BTC HOLDINGS</div>
              <div className="holding-amount">$91,000 <span className="currency">BTC</span></div>
              <div className="holding-change positive">
                <span className="change-icon">📈</span>
                +4.8% Today
              </div>
            </div>
          </div>

          <div className="holding-card">
            <div className="holding-icon sol">◎</div>
            <div className="holding-info">
              <div className="holding-label">SOLANA HOLDINGS</div>
              <div className="holding-amount">$124,000 <span className="currency">SOL</span></div>
              <div className="holding-change positive">
                <span className="change-icon">📈</span>
                +4.8% Today
              </div>
            </div>
          </div>

          <div className="holding-card">
            <div className="holding-icon usdc">$</div>
            <div className="holding-info">
              <div className="holding-label">USDC HOLDINGS</div>
              <div className="holding-amount">$51,500 <span className="currency">USDC</span></div>
              <div className="holding-change neutral">
                <span className="change-icon">📊</span>
                0.0% Today
              </div>
            </div>
          </div>

          <div className="holding-card">
            <div className="holding-icon esvc">囲</div>
            <div className="holding-info">
              <div className="holding-label">ESVC RESERVES</div>
              <div className="holding-amount">$51,500 <span className="currency">ESVC</span></div>
              <div className="holding-change positive">
                <span className="change-icon">📈</span>
                -0.8% Today
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="dashboard-actions">
          <button className="btn-primary" onClick={handleStartStaking}>
            Start Staking Now
            <span className="cta-icon">🚀</span>
          </button>
          <button className="btn-secondary" onClick={handleViewTreasuryDashboard}>
            View Treasury Dashboard
            <span className="arrow">→</span>
          </button>
        </div>
      </div>
    </section>
  );
};

export default TreasuryDashboard;
