/* Treasury Dropdown Component */
.treasury-dropdown-container {
  position: relative;
  display: inline-block;
  min-width: 160px;
}

/* Dropdown Trigger */
.treasury-dropdown-trigger {
  width: 100%;
  background: rgba(38, 38, 38, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px 16px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  transition: all 0.3s ease;
  outline: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.treasury-dropdown-trigger:hover {
  background: rgba(48, 48, 48, 0.9);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

.treasury-dropdown-trigger:focus {
  border-color: rgba(191, 65, 41, 0.5);
  box-shadow: 0 0 0 3px rgba(191, 65, 41, 0.1);
}

.treasury-dropdown-container.open .treasury-dropdown-trigger {
  border-color: rgba(191, 65, 41, 0.6);
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  background: rgba(48, 48, 48, 0.95);
}

/* Dropdown Text */
.dropdown-text {
  flex: 1;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Dropdown Arrow */
.dropdown-arrow {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
  flex-shrink: 0;
}

.dropdown-arrow img {
  width: 12px;
  height: 12px;
  filter: brightness(0) invert(1);
  opacity: 0.7;
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

.dropdown-arrow.open img {
  opacity: 1;
}

/* Dropdown Menu */
.treasury-dropdown-menu {
  position: absolute;
  top: calc(100% - 1px);
  left: 0;
  right: 0;
  background: rgba(38, 38, 38, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(191, 65, 41, 0.6);
  border-top: none;
  border-radius: 0 0 8px 8px;
  overflow: hidden;
  z-index: 9999;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  animation: treasuryDropdownSlideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  max-height: 240px;
  overflow-y: auto;
}

@keyframes treasuryDropdownSlideDown {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Dropdown Options */
.treasury-dropdown-option {
  width: 100%;
  background: transparent;
  border: none;
  padding: 12px 16px;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  outline: none;
}

.treasury-dropdown-option:last-child {
  border-bottom: none;
}

.treasury-dropdown-option:hover {
  background: rgba(255, 255, 255, 0.08);
  color: #FFFFFF;
}

.treasury-dropdown-option:focus {
  background: rgba(255, 255, 255, 0.08);
  color: #FFFFFF;
  outline: 2px solid rgba(191, 65, 41, 0.3);
  outline-offset: -2px;
}

.treasury-dropdown-option.selected {
  background: rgba(191, 65, 41, 0.15);
  color: #FFFFFF;
  font-weight: 600;
}

.treasury-dropdown-option.selected:hover {
  background: rgba(191, 65, 41, 0.25);
}

/* Scrollbar for dropdown menu */
.treasury-dropdown-menu::-webkit-scrollbar {
  width: 4px;
}

.treasury-dropdown-menu::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.treasury-dropdown-menu::-webkit-scrollbar-thumb {
  background: rgba(191, 65, 41, 0.6);
  border-radius: 2px;
}

.treasury-dropdown-menu::-webkit-scrollbar-thumb:hover {
  background: rgba(191, 65, 41, 0.8);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .treasury-dropdown-container {
    min-width: auto;
    width: 100%;
  }

  .treasury-dropdown-trigger {
    padding: 14px 16px;
    font-size: 16px;
    min-height: 48px;
    box-sizing: border-box;
  }

  .dropdown-text {
    font-size: 16px;
  }

  .dropdown-arrow {
    width: 18px;
    height: 18px;
  }

  .dropdown-arrow img {
    width: 14px;
    height: 14px;
  }

  .treasury-dropdown-option {
    padding: 14px 16px;
    font-size: 16px;
    min-height: 48px;
    display: flex;
    align-items: center;
  }

  .treasury-dropdown-menu {
    max-height: 60vh;
  }
}

/* Extra small mobile devices */
@media (max-width: 480px) {
  .treasury-dropdown-trigger {
    padding: 12px 14px;
    font-size: 15px;
    min-height: 44px;
  }

  .dropdown-text {
    font-size: 15px;
  }

  .treasury-dropdown-option {
    padding: 12px 14px;
    font-size: 15px;
    min-height: 44px;
  }
}
