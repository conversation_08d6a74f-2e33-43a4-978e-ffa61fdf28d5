import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import '../styles/components/Header.css';
import logoEsvc from '../assets/logo-esvc.png';
import DashboardModeToggle from '../shared/DashboardModeToggle';
import { useDemoState } from '../../context/DemoStateContext';

interface HeaderProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const Header: React.FC<HeaderProps> = ({
  onNavigateToSignUp,
  onNavigateToLogin,
  onNavigateToLanding
}) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { userData } = useDemoState();
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  // Check if we should show the dashboard mode toggle (only on user dashboard pages)
  const shouldShowDashboardToggle = location.pathname.includes('/user-dashboard');



  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const handleMenuToggleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('Menu toggle clicked, current state:', isMobileMenuOpen);
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target as Node)) {
        closeMobileMenu();
      }
    };

    if (isMobileMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMobileMenuOpen]);

  return (
    <header className="header">
      <div className="container">
        <div className="header-content">
          {/* Logo */}
          <div className="logo" onClick={onNavigateToLanding} style={{ cursor: 'pointer' }}>
            <img src={logoEsvc} alt="ESVC Logo" className="logo-image" />
          </div>

          {/* Desktop Navigation */}
          <nav className="nav desktop-nav">
            <button onClick={onNavigateToLanding} className="nav-link active">Home</button>
            <button onClick={() => navigate('/stake-esvc')} className="nav-link">Stake ESVC</button>
            <button onClick={() => navigate('/user-dashboard/get-funding')} className="nav-link">Get Funding</button>
            <button onClick={() => navigate('/trade-challenge')} className="nav-link">Trade Challenge</button>
            <button onClick={() => navigate('/contact-us')} className="nav-link">Contact Us</button>
          </nav>

          {/* Desktop Action Buttons */}
          <div className="header-actions desktop-actions">
            <button className="btn-secondary" onClick={onNavigateToLogin}>Login</button>
            <button className="btn-primary" onClick={onNavigateToSignUp}>Get Started</button>
          </div>

          {/* Mobile Hamburger Menu */}
          <button
            className={`mobile-menu-toggle ${isMobileMenuOpen ? 'menu-open' : ''}`}
            onClick={handleMenuToggleClick}
            type="button"
            aria-label={isMobileMenuOpen ? 'Close menu' : 'Open menu'}
          >
            <span className="hamburger-line"></span>
            <span className="hamburger-line"></span>
            <span className="hamburger-line"></span>
          </button>
        </div>

        {/* Mobile Menu */}
        <div ref={mobileMenuRef} className={`mobile-menu ${isMobileMenuOpen ? 'mobile-menu-open' : ''}`}>
          <nav className="mobile-nav">
            <button onClick={() => { onNavigateToLanding?.(); closeMobileMenu(); }} className="mobile-nav-link">Home</button>
            <button onClick={() => { navigate('/stake-esvc'); closeMobileMenu(); }} className="mobile-nav-link">Stake ESVC</button>
            <button onClick={() => { navigate('/user-dashboard/get-funding'); closeMobileMenu(); }} className="mobile-nav-link">Get Funding</button>
            <button onClick={() => { navigate('/trade-challenge'); closeMobileMenu(); }} className="mobile-nav-link">Trade Challenge</button>
            <button onClick={() => { navigate('/contact-us'); closeMobileMenu(); }} className="mobile-nav-link">Contact Us</button>
          </nav>
          <div className="mobile-actions">
            <button className="btn-primary mobile-btn" onClick={() => { onNavigateToSignUp?.(); closeMobileMenu(); }}>Get Started</button>
            <button className="mobile-login" onClick={() => { onNavigateToLogin?.(); closeMobileMenu(); }}>Login</button>
          </div>
        </div>
      </div>

      {/* Dashboard Mode Toggle - Show only on user dashboard pages and when user is logged in */}
      {shouldShowDashboardToggle && userData.isLoggedIn && (
        <DashboardModeToggle className="header-toggle" />
      )}
    </header>
  );
};

export default Header;
