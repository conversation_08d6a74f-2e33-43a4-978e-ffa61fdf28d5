"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/components/TreasuryDashboard.tsx":
/*!**************************************************!*\
  !*** ./src/app/components/TreasuryDashboard.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TreasuryDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction TreasuryDashboard() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const handleStartStaking = ()=>{\n        // Navigate to staking page\n        router.push('/user-dashboard/my-stake');\n    };\n    const handleViewTreasuryDashboard = ()=>{\n        // Navigate to Treasury Dashboard page\n        router.push('/treasury-dashboard');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative w-full bg-gradient-to-br from-[#1a1a1a] via-[#1f1f1f] to-[#1a1a1a] py-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute w-[239px] h-[239px] top-0 right-0 lg:right-0 bg-[#d19049] rounded-full blur-[100px] opacity-30\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute w-[239px] h-[239px] top-[232px] left-[22px] lg:left-[22px] bg-[#cc6754] rounded-full blur-[100px] opacity-30\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 right-0 bottom-0 bg-gradient-radial from-[rgba(232,90,79,0.05)] via-transparent to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 right-0 bottom-0 bg-gradient-radial from-[rgba(212,175,55,0.05)] via-transparent to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container max-w-[1200px] mx-auto px-5 relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-15\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-5xl md:text-6xl font-bold text-white mb-4 font-montserrat\",\n                                children: \"Treasury Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"max-w-[600px] mx-auto text-lg text-neutral-300 leading-relaxed font-montserrat\",\n                                children: [\n                                    \"Live insights into how funds are held, ROI is distributed, and startups are supported.\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Empowering you to stake with trust.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-15\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-neutral-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6 transition-all duration-300 hover:transform hover:-translate-y-1 hover:shadow-2xl hover:border-[#bf4129]/30 min-h-[160px] flex flex-col justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs font-semibold text-neutral-300 uppercase tracking-wider font-montserrat\",\n                                            children: \"TOTAL CAPITAL INVESTED\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl font-bold text-white mb-2 font-montserrat\",\n                                        children: \"$266,500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400 text-sm\",\n                                                children: \"\\uD83D\\uDCC8\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400 text-sm font-semibold font-montserrat\",\n                                                children: \"+4.8% Today\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-neutral-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6 transition-all duration-300 hover:transform hover:-translate-y-1 hover:shadow-2xl hover:border-[#bf4129]/30 min-h-[160px] flex flex-col justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs font-semibold text-neutral-300 uppercase tracking-wider font-montserrat\",\n                                            children: \"TOTAL PROFIT GENERATED\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl font-bold text-white mb-2 font-montserrat\",\n                                        children: \"$43,700\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400 text-sm\",\n                                                children: \"\\uD83D\\uDCC8\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400 text-sm font-semibold font-montserrat\",\n                                                children: \"+4.8% Today\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-neutral-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6 transition-all duration-300 hover:transform hover:-translate-y-1 hover:shadow-2xl hover:border-[#bf4129]/30 min-h-[160px] flex flex-col justify-center md:col-span-2 xl:col-span-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs font-semibold text-neutral-300 uppercase tracking-wider font-montserrat\",\n                                                children: \"FUNDS AVAILABLE TO FUND STARTUPS\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-neutral-300 opacity-70 text-sm\",\n                                                children: \"ℹ️\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl font-bold text-white mb-2 font-montserrat\",\n                                        children: \"$2,185\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400 text-sm\",\n                                                children: \"\\uD83D\\uDCC8\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400 text-sm font-semibold font-montserrat\",\n                                                children: \"+4.8% Today\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-15\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-neutral-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6 flex items-center gap-5 transition-all duration-300 hover:transform hover:-translate-y-1 hover:shadow-2xl hover:border-[#bf4129]/30 min-h-[160px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 rounded-full bg-gradient-to-br from-[#f7931a] to-[#ffb347] flex items-center justify-center text-white font-bold text-2xl flex-shrink-0 transition-transform duration-300 hover:scale-110\",\n                                        children: \"₿\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs font-semibold text-neutral-300 uppercase tracking-wider mb-1 font-montserrat\",\n                                                children: \"BTC HOLDINGS\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xl font-bold text-white mb-1 font-montserrat\",\n                                                children: [\n                                                    \"$91,000 \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-neutral-400 font-medium\",\n                                                        children: \"BTC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400 text-xs\",\n                                                        children: \"\\uD83D\\uDCC8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                        lineNumber: 93,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400 text-xs font-semibold font-montserrat\",\n                                                        children: \"+4.8% Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-neutral-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6 flex items-center gap-5 transition-all duration-300 hover:transform hover:-translate-y-1 hover:shadow-2xl hover:border-[#bf4129]/30 min-h-[160px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 rounded-full bg-gradient-to-br from-[#9945ff] to-[#14f195] flex items-center justify-center text-white font-bold text-2xl flex-shrink-0 transition-transform duration-300 hover:scale-110\",\n                                        children: \"◎\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs font-semibold text-neutral-300 uppercase tracking-wider mb-1 font-montserrat\",\n                                                children: \"SOLANA HOLDINGS\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xl font-bold text-white mb-1 font-montserrat\",\n                                                children: [\n                                                    \"$124,000 \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-neutral-400 font-medium\",\n                                                        children: \"SOL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                        lineNumber: 107,\n                                                        columnNumber: 26\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400 text-xs\",\n                                                        children: \"\\uD83D\\uDCC8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400 text-xs font-semibold font-montserrat\",\n                                                        children: \"+4.8% Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-neutral-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6 flex items-center gap-5 transition-all duration-300 hover:transform hover:-translate-y-1 hover:shadow-2xl hover:border-[#bf4129]/30 min-h-[160px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 rounded-full bg-gradient-to-br from-[#2775ca] to-[#4dabf7] flex items-center justify-center text-white font-bold text-2xl flex-shrink-0 transition-transform duration-300 hover:scale-110\",\n                                        children: \"$\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs font-semibold text-neutral-300 uppercase tracking-wider mb-1 font-montserrat\",\n                                                children: \"USDC HOLDINGS\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xl font-bold text-white mb-1 font-montserrat\",\n                                                children: [\n                                                    \"$51,500 \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-neutral-400 font-medium\",\n                                                        children: \"USDC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-neutral-400 text-xs\",\n                                                        children: \"\\uD83D\\uDCCA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-neutral-400 text-xs font-semibold font-montserrat\",\n                                                        children: \"0.0% Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-neutral-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6 flex items-center gap-5 transition-all duration-300 hover:transform hover:-translate-y-1 hover:shadow-2xl hover:border-[#bf4129]/30 min-h-[160px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 rounded-full bg-gradient-to-br from-[#d19049] to-[#ff6b5b] flex items-center justify-center text-white font-bold text-2xl flex-shrink-0 transition-transform duration-300 hover:scale-110\",\n                                        children: \"囲\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs font-semibold text-neutral-300 uppercase tracking-wider mb-1 font-montserrat\",\n                                                children: \"ESVC RESERVES\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xl font-bold text-white mb-1 font-montserrat\",\n                                                children: [\n                                                    \"$51,500 \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-neutral-400 font-medium\",\n                                                        children: \"ESVC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400 text-xs\",\n                                                        children: \"\\uD83D\\uDCC8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400 text-xs font-semibold font-montserrat\",\n                                                        children: \"-0.8% Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-center items-center gap-6 mt-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleStartStaking,\n                                className: \"inline-flex items-center justify-center gap-3 px-8 py-4 bg-[#bf4129] hover:bg-[#a83a25] text-white font-semibold text-lg rounded-full transition-all duration-300 hover:transform hover:scale-105 hover:shadow-xl w-full md:w-auto font-montserrat\",\n                                children: [\n                                    \"Start Staking Now\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl\",\n                                        children: \"\\uD83D\\uDE80\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleViewTreasuryDashboard,\n                                className: \"inline-flex items-center justify-center gap-3 px-8 py-4 bg-transparent border border-neutral-700 hover:border-neutral-500 text-white font-semibold text-lg rounded-full transition-all duration-300 hover:transform hover:scale-105 hover:shadow-xl w-full md:w-auto font-montserrat group\",\n                                children: [\n                                    \"View Treasury Dashboard\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg transition-transform duration-300 group-hover:translate-x-1\",\n                                        children: \"→\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\workspace\\\\.typescript\\\\esvc\\\\esvc\\\\src\\\\app\\\\components\\\\TreasuryDashboard.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n_s(TreasuryDashboard, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter\n    ];\n});\n_c = TreasuryDashboard;\nvar _c;\n$RefreshReg$(_c, \"TreasuryDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/TreasuryDashboard.tsx\n"));

/***/ })

});