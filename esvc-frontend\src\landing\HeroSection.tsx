import React from 'react';

export default function HeroSection() {
  return (
    <section className="relative w-full">
      {/* Background blur effects */}
      <div className="absolute w-[239px] h-[239px] top-0 lg:right-0 bg-[#d19049] rounded-[119.5px] blur-100 opacity-30"></div>
      <div className="absolute w-[239px] h-[239px] top-[232px] lg:left-[22px] bg-[#cc6754] rounded-[119.5px] blur-100 opacity-30"></div>
      
      <section className="flex flex-col items-center gap-8 w-full max-w-4xl mx-auto mb-20 py-5 px-5 lg:px-0">
        <div className="flex flex-col items-center gap-4 w-full text-center relative">
          <div className="inline-flex items-center justify-center gap-2.5 px-4 rounded-full"></div>
          <div className="border border-neutral-700 rounded-full p-3 bg-gradient-to-r from-[#BF4129] to-[#D19049] bg-clip-text text-transparent">
            <h3>Earn 20% annual returns</h3>
          </div>
          
          <h1 className="font-montserrat font-bold md:text-[47px] text-[33px]">
            <span className="text-[#cc6754]">Grow</span>
            <span className="text-neutral-100"> Your Wealth. </span>
            <span className="text-[#d19049]">Get</span>
            <span className="text-neutral-100"> Funded. <br/> Stake and Earn .</span>
          </h1>
          
          <p className="max-w-[627px] font-montserrat font-normal text-neutral-300 text-base leading-6">
            Stake your ESVC tokens and earn daily ROI. <br/>
            Unlock exclusive opportunities to pitch your startup ideas for funding/get funded trade capital. <br/>
            Earn from our Bitcoin/Crypto Treasury
          </p>
          
          {/* Decorative elements - using placeholder images for now */}
          <img
            className="top-[220px] w-[200px] h-[13px] absolute md:top-[210px] md:w-[300px] right-1 md:right-1 md:-translate-x-1/2"
            alt="Decorative line"
            src="/api/placeholder/300/13"
            width={300}
            height={13}
          />
          <img
            className="absolute w-44 h-[50px] top-[179px] left-[20px] md:w-44 md:h-[75px] md:top-[150px] md:left-[220px]"
            alt="Vector graphic circle"
            src="/api/placeholder/176/75"
            width={176}
            height={75}
          />
        </div>
        
        <button className="whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 text-primary-foreground shadow h-14 bg-[#bf4129] hover:bg-[#a83a25] rounded-full px-4 py-2.5 flex items-center justify-center gap-3">
          <span className="font-montserrat font-semibold text-neutral-50 text-lg">Start Staking Now</span>
          <img
            className="w-6 h-6"
            alt="Rocket launch"
            src="/api/placeholder/24/24"
            width={24}
            height={24}
          />
        </button>
      </section>
      
      {/* Decorative coin images - using placeholder images for now */}
      <img
        className="absolute w-[106px] top-[401px] right-[-10px] md:w-[296px] md:h-[296px] md:top-[187px] md:right-[5px] object-cover"
        alt="Decorative coin image gold"
        src="/api/placeholder/296/296"
        width={296}
        height={296}
      />
      <img
        className="w-[106px] h-[106px] top-[401px] left-0 md:w-[370px] md:h-[370px] md:top-[179px] md:left-0 absolute object-cover"
        alt="Decorative coin image red big"
        src="/api/placeholder/370/370"
        width={370}
        height={370}
      />
      <img
        className="w-[78px] h-[78px] top-[492px] right-[20px] md:w-[156px] md:h-[156px] md:top-[352px] md:right-[256px] absolute object-cover"
        alt="Decorative coin image red small right"
        src="/api/placeholder/156/156"
        width={156}
        height={156}
      />
      <img
        className="absolute w-[94px] h-[94px] top-[492px] left-[10px] md:w-[188px] md:h-[188px] md:top-[352px] md:left-[276px] object-cover"
        alt="Decorative coin image"
        src="/api/placeholder/188/188"
        width={188}
        height={188}
      />
    </section>
  );
}
