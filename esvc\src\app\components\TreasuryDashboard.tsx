'use client';

import Image from "next/image";
import { useRouter } from "next/navigation";

export default function TreasuryDashboard() {
  const router = useRouter();

  const handleStartStaking = () => {
    // Navigate to staking page
    router.push('/user-dashboard/my-stake');
  };

  const handleViewTreasuryDashboard = () => {
    // Navigate to Treasury Dashboard page
    router.push('/treasury-dashboard');
  };

  return (
    <section className="relative w-full bg-gradient-to-br from-[#1a1a1a] via-[#1f1f1f] to-[#1a1a1a] py-20">
      {/* Background effects */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute w-[239px] h-[239px] top-0 right-0 lg:right-0 bg-[#d19049] rounded-full blur-[100px] opacity-30"></div>
        <div className="absolute w-[239px] h-[239px] top-[232px] left-[22px] lg:left-[22px] bg-[#cc6754] rounded-full blur-[100px] opacity-30"></div>
        <div className="absolute top-0 left-0 right-0 bottom-0 bg-gradient-radial from-[rgba(232,90,79,0.05)] via-transparent to-transparent"></div>
        <div className="absolute top-0 left-0 right-0 bottom-0 bg-gradient-radial from-[rgba(212,175,55,0.05)] via-transparent to-transparent"></div>
      </div>

      <div className="container max-w-[1200px] mx-auto px-5 relative z-10">
        {/* Header */}
        <div className="text-center mb-15">
          <h2 className="text-5xl md:text-6xl font-bold text-white mb-4 font-montserrat">Treasury Dashboard</h2>
          <p className="max-w-[600px] mx-auto text-lg text-neutral-300 leading-relaxed font-montserrat">
            Live insights into how funds are held, ROI is distributed, and startups are supported.
            <br />
            Empowering you to stake with trust.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-15">
          {/* Total Capital Invested */}
          <div className="bg-neutral-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6 transition-all duration-300 hover:transform hover:-translate-y-1 hover:shadow-2xl hover:border-[#bf4129]/30 min-h-[160px] flex flex-col justify-center">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-xs font-semibold text-neutral-300 uppercase tracking-wider font-montserrat">TOTAL CAPITAL INVESTED</span>
            </div>
            <div className="text-4xl font-bold text-white mb-2 font-montserrat">$266,500</div>
            <div className="flex items-center gap-1">
              <span className="text-green-400 text-sm">📈</span>
              <span className="text-green-400 text-sm font-semibold font-montserrat">+4.8% Today</span>
            </div>
          </div>

          {/* Total Profit Generated */}
          <div className="bg-neutral-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6 transition-all duration-300 hover:transform hover:-translate-y-1 hover:shadow-2xl hover:border-[#bf4129]/30 min-h-[160px] flex flex-col justify-center">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-xs font-semibold text-neutral-300 uppercase tracking-wider font-montserrat">TOTAL PROFIT GENERATED</span>
            </div>
            <div className="text-4xl font-bold text-white mb-2 font-montserrat">$43,700</div>
            <div className="flex items-center gap-1">
              <span className="text-green-400 text-sm">📈</span>
              <span className="text-green-400 text-sm font-semibold font-montserrat">+4.8% Today</span>
            </div>
          </div>

          {/* Funds Available */}
          <div className="bg-neutral-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6 transition-all duration-300 hover:transform hover:-translate-y-1 hover:shadow-2xl hover:border-[#bf4129]/30 min-h-[160px] flex flex-col justify-center md:col-span-2 xl:col-span-1">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-xs font-semibold text-neutral-300 uppercase tracking-wider font-montserrat">FUNDS AVAILABLE TO FUND STARTUPS</span>
              <span className="text-neutral-300 opacity-70 text-sm">ℹ️</span>
            </div>
            <div className="text-4xl font-bold text-white mb-2 font-montserrat">$2,185</div>
            <div className="flex items-center gap-1">
              <span className="text-green-400 text-sm">📈</span>
              <span className="text-green-400 text-sm font-semibold font-montserrat">+4.8% Today</span>
            </div>
          </div>
        </div>

        {/* Holdings Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-15">
          {/* BTC Holdings */}
          <div className="bg-neutral-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6 flex items-center gap-5 transition-all duration-300 hover:transform hover:-translate-y-1 hover:shadow-2xl hover:border-[#bf4129]/30 min-h-[160px]">
            <div className="w-12 h-12 rounded-full bg-gradient-to-br from-[#f7931a] to-[#ffb347] flex items-center justify-center text-white font-bold text-2xl flex-shrink-0 transition-transform duration-300 hover:scale-110">
              ₿
            </div>
            <div className="flex-1">
              <div className="text-xs font-semibold text-neutral-300 uppercase tracking-wider mb-1 font-montserrat">BTC HOLDINGS</div>
              <div className="text-xl font-bold text-white mb-1 font-montserrat">
                $91,000 <span className="text-sm text-neutral-400 font-medium">BTC</span>
              </div>
              <div className="flex items-center gap-1">
                <span className="text-green-400 text-xs">📈</span>
                <span className="text-green-400 text-xs font-semibold font-montserrat">+4.8% Today</span>
              </div>
            </div>
          </div>

          {/* Solana Holdings */}
          <div className="bg-neutral-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6 flex items-center gap-5 transition-all duration-300 hover:transform hover:-translate-y-1 hover:shadow-2xl hover:border-[#bf4129]/30 min-h-[160px]">
            <div className="w-12 h-12 rounded-full bg-gradient-to-br from-[#9945ff] to-[#14f195] flex items-center justify-center text-white font-bold text-2xl flex-shrink-0 transition-transform duration-300 hover:scale-110">
              ◎
            </div>
            <div className="flex-1">
              <div className="text-xs font-semibold text-neutral-300 uppercase tracking-wider mb-1 font-montserrat">SOLANA HOLDINGS</div>
              <div className="text-xl font-bold text-white mb-1 font-montserrat">
                $124,000 <span className="text-sm text-neutral-400 font-medium">SOL</span>
              </div>
              <div className="flex items-center gap-1">
                <span className="text-green-400 text-xs">📈</span>
                <span className="text-green-400 text-xs font-semibold font-montserrat">+4.8% Today</span>
              </div>
            </div>
          </div>

          {/* USDC Holdings */}
          <div className="bg-neutral-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6 flex items-center gap-5 transition-all duration-300 hover:transform hover:-translate-y-1 hover:shadow-2xl hover:border-[#bf4129]/30 min-h-[160px]">
            <div className="w-12 h-12 rounded-full bg-gradient-to-br from-[#2775ca] to-[#4dabf7] flex items-center justify-center text-white font-bold text-2xl flex-shrink-0 transition-transform duration-300 hover:scale-110">
              $
            </div>
            <div className="flex-1">
              <div className="text-xs font-semibold text-neutral-300 uppercase tracking-wider mb-1 font-montserrat">USDC HOLDINGS</div>
              <div className="text-xl font-bold text-white mb-1 font-montserrat">
                $51,500 <span className="text-sm text-neutral-400 font-medium">USDC</span>
              </div>
              <div className="flex items-center gap-1">
                <span className="text-neutral-400 text-xs">📊</span>
                <span className="text-neutral-400 text-xs font-semibold font-montserrat">0.0% Today</span>
              </div>
            </div>
          </div>

          {/* ESVC Reserves */}
          <div className="bg-neutral-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6 flex items-center gap-5 transition-all duration-300 hover:transform hover:-translate-y-1 hover:shadow-2xl hover:border-[#bf4129]/30 min-h-[160px]">
            <div className="w-12 h-12 rounded-full bg-gradient-to-br from-[#d19049] to-[#ff6b5b] flex items-center justify-center text-white font-bold text-2xl flex-shrink-0 transition-transform duration-300 hover:scale-110">
              囲
            </div>
            <div className="flex-1">
              <div className="text-xs font-semibold text-neutral-300 uppercase tracking-wider mb-1 font-montserrat">ESVC RESERVES</div>
              <div className="text-xl font-bold text-white mb-1 font-montserrat">
                $51,500 <span className="text-sm text-neutral-400 font-medium">ESVC</span>
              </div>
              <div className="flex items-center gap-1">
                <span className="text-green-400 text-xs">📈</span>
                <span className="text-green-400 text-xs font-semibold font-montserrat">-0.8% Today</span>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col md:flex-row justify-center items-center gap-6 mt-10">
          <button
            onClick={handleStartStaking}
            className="inline-flex items-center justify-center gap-3 px-8 py-4 bg-[#bf4129] hover:bg-[#a83a25] text-white font-semibold text-lg rounded-full transition-all duration-300 hover:transform hover:scale-105 hover:shadow-xl w-full md:w-auto font-montserrat"
          >
            Start Staking Now
            <span className="text-xl">🚀</span>
          </button>
          <button
            onClick={handleViewTreasuryDashboard}
            className="inline-flex items-center justify-center gap-3 px-8 py-4 bg-transparent border border-neutral-700 hover:border-neutral-500 text-white font-semibold text-lg rounded-full transition-all duration-300 hover:transform hover:scale-105 hover:shadow-xl w-full md:w-auto font-montserrat group"
          >
            View Treasury Dashboard
            <span className="text-lg transition-transform duration-300 group-hover:translate-x-1">→</span>
          </button>
        </div>
      </div>
    </section>
  );
}
