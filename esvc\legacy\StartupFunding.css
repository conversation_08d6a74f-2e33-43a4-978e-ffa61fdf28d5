/* Startup Funding Content */
.startup-funding-content {
  position: relative;
  z-index: 2;
}

/* Page Header - Reuse from Overview */
.page-header {
  text-align: center;
  padding: 60px 40px 40px;
  max-width: 800px;
  margin: 0 auto;
}

.page-title {
  font-size: 64px;
  font-weight: 600;
  margin: 0 0 24px 0;
  color: #FFFFFF;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
  gap: 20px;
}

.title-with-span {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.title-span {
  max-width: 300px;
  height: auto;
}

.page-subtitle {
  font-size: 18px;
  color: #CCCCCC;
  line-height: 28px;
  margin: 0;
}

/* Dashboard Layout - Reuse from Overview */
.dashboard-layout {
  display: flex;
  gap: 40px;
  padding: 0 40px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Sidebar styles removed - using SideNav component */

/* Dashboard Content Wrapper - Borrowed from UserOverview */
.dashboard-content-wrapper {
  display: flex;
  gap: 40px;
  width: 100%;
}

/* Dashboard Content */
.dashboard-content {
  flex: 1;
  max-width: 920px;
}

/* Section Header */
.funding-header {
  margin-bottom: 32px;
}

.section-title {
  font-size: 32px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0;
}

/* Stats Cards */
.funding-stats {
  display: flex;
  gap: 24px;
  margin-top: -42px;
  margin-bottom: 40px;
}

.stats-card {
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 24px;
  flex: 1;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.stats-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  font-weight: 600;
  color: #999999;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.stats-value {
  font-family: 'Montserrat', sans-serif;
  font-size: 32px;
  font-weight: 700;
  color: #FFFFFF;
  margin-bottom: 16px;
}

.stats-sublabel {
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  font-weight: 600;
  color: #999999;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.stats-subvalue {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 700;
  color: #FFFFFF;
}

/* Tooltip */
.tooltip-container {
  position: relative;
  display: inline-block;
}

.info-icon {
  width: 16px;
  height: 16px;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.info-icon:hover {
  opacity: 1;
}

.tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: #FFFFFF;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 400;
  line-height: 1.4;
  white-space: nowrap;
  max-width: 280px;
  white-space: normal;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
  margin-bottom: 8px;
}

.tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
}

.tooltip-container:hover .tooltip {
  opacity: 1;
  visibility: visible;
}

/* Stats Change */
.stats-change {
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #4AFF4A;
}

.trend-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

/* Funding History */
.funding-history {
  /* Remove outer card styling */
}

/* Filters */
.funding-filters {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  align-items: center;
}

.search-filter {
  flex: 1;
}

.search-input {
  width: 100%;
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 12px 16px 12px 44px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 400;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23999999'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'/%3e%3c/svg%3e");
  background-position: 16px center;
  background-repeat: no-repeat;
  background-size: 16px;
}

.search-input::placeholder {
  color: #999999;
}

.search-input:focus {
  outline: none;
  border-color: rgba(191, 65, 41, 0.5);
}

.dropdown-filters {
  display: flex;
  gap: 12px;
}

.filter-select {
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 12px 40px 12px 44px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  appearance: none;
  min-width: 180px;
  background-image:
    url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23999999'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 6h16M4 12h16M4 18h16'/%3e%3c/svg%3e"),
    url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23FFFFFF' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: 16px center, right 12px center;
  background-repeat: no-repeat, no-repeat;
  background-size: 16px, 16px;
}

.filter-select:focus {
  outline: none;
  border-color: rgba(191, 65, 41, 0.5);
}

/* Table */
.funding-table {
  margin-bottom: 32px;
}

.table-header {
  display: grid;
  grid-template-columns: 1fr 120px 140px 140px 120px;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 8px;
}

.table-row {
  display: grid;
  grid-template-columns: 1fr 120px 140px 140px 120px;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  align-items: center;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #FFFFFF;
}

.table-header .table-cell {
  font-weight: 600;
  color: #CCCCCC;
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 0.5px;
}

/* Stage Badges */
.stage-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stage-badge.mvp {
  background: rgba(255, 193, 7, 0.2);
  color: #FFC107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.stage-badge.launched {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.stage-badge.seed {
  background: rgba(33, 150, 243, 0.2);
  color: #2196F3;
  border: 1px solid rgba(33, 150, 243, 0.3);
}

.stage-badge.series-a {
  background: rgba(156, 39, 176, 0.2);
  color: #9C27B0;
  border: 1px solid rgba(156, 39, 176, 0.3);
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
  gap: 16px;
}

.pagination-btn {
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px 16px;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-btn:hover:not(.disabled) {
  background: rgba(255, 255, 255, 0.05);
  color: #FFFFFF;
}

.pagination-btn.active {
  background: linear-gradient(135deg, #BF4129 0%, #D19049 100%);
  color: #FFFFFF;
  border-color: rgba(191, 65, 41, 0.3);
}

.pagination-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-center {
  display: flex;
  gap: 8px;
  align-items: center;
}

.pagination-ellipsis {
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  padding: 0 8px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .page-header {
    padding: 40px 20px 30px;
  }

  .page-title {
    font-size: 42px;
    gap: 12px;
    justify-content: center;
  }

  .title-with-span {
    align-items: center;
  }

  .title-span {
    max-width: 180px;
  }

  .page-subtitle {
    font-size: 14px;
  }

  .dashboard-layout {
    flex-direction: column;
    padding: 0 20px;
    gap: 24px;
  }

  /* Mobile sidebar styles removed - SideNav component handles its own mobile styles */

  .dashboard-content-wrapper {
    flex-direction: column;
    gap: 16px;
  }

  .dashboard-content {
    order: 2;
    max-width: 100%;
    width: 100%;
  }

  .funding-stats {
    flex-direction: column;
    gap: 16px;
  }

  .stats-card {
    padding: 20px;
  }

  .funding-filters {
    flex-direction: column;
    gap: 16px;
  }

  .search-filter {
    order: 1;
  }

  .search-input {
    width: 100%;
    padding: 12px 16px 12px 44px;
  }

  .dropdown-filters {
    order: 2;
    flex-direction: row;
    gap: 12px;
  }

  .filter-select {
    flex: 1;
    min-width: auto;
    padding: 12px 40px 12px 44px;
  }

  .funding-history {
    padding: 0;
  }

  /* Mobile Table - List Layout */
  .table-header {
    display: none;
  }

  .table-row {
    display: block;
    padding: 20px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: none;
    border-radius: 0;
    margin-bottom: 0;
    border-left: none;
    border-right: none;
    border-top: none;
  }

  .table-row:last-child {
    border-bottom: none;
  }

  .table-cell {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 14px;
  }

  .table-cell:last-child {
    margin-bottom: 0;
  }

  .table-cell:first-child {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    display: block;
  }

  .table-cell:not(:first-child)::before {
    content: attr(data-label);
    font-weight: 500;
    color: #CCCCCC;
    font-size: 14px;
  }

  .pagination {
    gap: 16px;
    flex-direction: row;
    justify-content: space-between;
  }

  .pagination-center {
    order: 0;
    justify-content: center;
    flex: 1;
    display: flex;
    align-items: center;
    font-family: 'Montserrat', sans-serif;
    font-size: 14px;
    color: #CCCCCC;
  }

  .pagination-center .pagination-btn,
  .pagination-center .pagination-ellipsis {
    display: none;
  }

  .pagination-center::before {
    content: 'Page 1/10';
  }

  .pagination-btn {
    padding: 10px 16px;
    font-size: 13px;
    min-width: auto;
    height: 40px;
    flex-shrink: 0;
  }
}
