/* Header - Exact Figma Design */
.header {
  position: fixed;
  top: 24px;
  left: 80px;
  right: 80px;
  height: 72px;
  background: transparent;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 999px;
  z-index: 1000;
  box-sizing: border-box;
}

.header-content {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 12px 40px;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
}

/* Logo */
.logo {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: auto;
  height: 38px;
  flex: 0 0 auto;
  margin-right: 20px;
}

.logo-image {
  height: 38px;
  width: auto;
  object-fit: contain;
}

/* Navigation Links */
.nav {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 0px;
  gap: 32px;
  height: 28px;
  flex: 1;
}

.nav-link {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 4px 8px;
  gap: 10px;
  height: 28px;
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 20px;
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: 999px;
  transition: all 0.3s ease;
  flex: none;
  flex-grow: 0;
}

.nav-link:hover {
  color: var(--text-primary);
  background: rgba(64, 64, 64, 0.5);
}

.nav-link.active {
  background: var(--border-color);
  border: 1px solid var(--border-secondary);
  color: var(--text-primary);
  font-weight: 500;
  width: 66px;
}

/* Header Actions - Buttons */
.header-actions {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  padding: 0px;
  gap: 12px;
  height: 48px;
  flex: 0 0 auto;
}

.header-actions .btn-secondary {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 16px;
  gap: 12px;
  width: 79px;
  height: 48px;
  border: 1px solid var(--border-color);
  border-radius: 999px;
  background: transparent;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.header-actions .btn-primary {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 20px;
  gap: 12px;
  min-width: 140px;
  height: 48px;
  background: var(--accent-orange);
  border-radius: 999px;
  border: none;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: #FAFAFA;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  flex: none;
  order: 1;
  flex-grow: 0;
}

.header-actions .btn-secondary:hover {
  border-color: var(--accent-orange);
  color: var(--accent-orange);
}

.header-actions .btn-primary:hover {
  background: var(--accent-orange-hover);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(191, 65, 41, 0.3);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 4px;
  position: relative;
  z-index: 1001; /* Ensure it stays above the mobile menu */
  outline: none; /* Remove focus outline */
  -webkit-tap-highlight-color: transparent; /* Remove tap highlight on mobile */
}

.hamburger-line {
  width: 20px;
  height: 2px;
  background: var(--text-primary);
  margin: 2px 0;
  transition: all 0.3s ease;
  border-radius: 1px;
  transform-origin: center;
  pointer-events: none; /* Allow clicks to pass through to the button */
}

/* Hamburger to X animation */
.mobile-menu-toggle.menu-open .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.menu-open .hamburger-line:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.menu-open .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Menu */
.mobile-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #1a1a1a;
  border-radius: 0 0 20px 20px;
  padding: 20px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  transform: translateY(-10px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-menu-open {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.mobile-nav {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.mobile-nav-link {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: var(--text-secondary);
  text-decoration: none;
  padding: 12px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
  text-align: center;
}

.mobile-nav-link:hover {
  background: rgba(64, 64, 64, 0.5);
  color: var(--text-primary);
}

.mobile-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

.mobile-btn {
  width: 100%;
  max-width: 200px;
  padding: 12px 24px;
  background: var(--accent-orange);
  color: #FAFAFA;
  border: none;
  border-radius: 999px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mobile-btn:hover {
  background: var(--accent-orange-hover);
}

.mobile-login {
  background: transparent;
  color: var(--text-primary);
  border: none;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.mobile-login:hover {
  color: var(--accent-orange);
}

/* Responsive Design */
/* Big screens - shift logo left and login/register right */
@media (min-width: 1201px) {
  .logo {
    transform: translateX(-110px);
  }

  .header-actions {
    transform: translateX(140px);
  }
}

@media (max-width: 1200px) {
  .header {
    left: 60px;
    right: 60px;
  }

  .header-content {
    gap: 150px;
  }

  .nav {
    width: 400px;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .header {
    left: 20px;
    right: 20px;
    height: 60px;
  }

  .header-content {
    padding: 8px 16px;
    gap: 20px;
  }

  .logo {
    width: auto;
    margin-right: 0;
  }

  .logo-image {
    height: 30px;
  }

  /* Hide desktop navigation and actions */
  .desktop-nav,
  .desktop-actions {
    display: none;
  }

  /* Show mobile menu toggle */
  .mobile-menu-toggle {
    display: flex;
  }

  /* Show mobile menu */
  .mobile-menu {
    display: block;
  }
}

@media (max-width: 480px) {
  .header {
    left: 10px;
    right: 10px;
    top: 12px;
  }

  .logo-image {
    height: 24px;
  }

  .mobile-menu {
    padding: 16px;
  }

  .mobile-nav-link {
    padding: 10px 12px;
    font-size: 14px;
  }
}
