import React from 'react';
import Header from './Header';
import Footer from './Footer';

interface PageLayoutProps {
  children: React.ReactNode;
  className?: string;
}

const PageLayout: React.FC<PageLayoutProps> = ({ children, className = '' }) => {
  return (
    <div className="w-full bg-neutral-900">
      <Header />
      <main className={`w-full min-h-screen bg-neutral-900 relative overflow-hidden ${className}`}>
        {/* Background blur effects - positioned behind content */}
        <div className="absolute inset-0 z-0">
          <div className="absolute lg:w-[239px] lg:h-[239px] lg:top-[280px] lg:right-0 bg-[#d19049] rounded-[119.5px] blur-[100px] opacity-30 pointer-events-none"></div>
          <div className="absolute top-[-70px] pointer-events-none">
            <svg xmlns="http://www.w3.org/2000/svg" width="204" height="306" viewBox="0 0 204 306" fill="none">
              <g opacity="0.3" filter="url(#filter0_f_644_12575)">
                <circle cx="51" cy="153" r="57" fill="#CC6754"></circle>
              </g>
              <defs>
                <filter id="filter0_f_644_12575" x="-101.397" y="0.602509" width="304.795" height="304.795" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                  <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
                  <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feBlend>
                  <feGaussianBlur stdDeviation="47.6987" result="effect1_foregroundBlur_644_12575"></feGaussianBlur>
                </filter>
              </defs>
            </svg>
          </div>
          <div className="absolute left-[195px] top-90 rounded-full pointer-events-none">
            <svg xmlns="http://www.w3.org/2000/svg" width="344" height="344" viewBox="0 0 344 344" fill="none">
              <g opacity="0.3" filter="url(#filter0_f_644_12578)">
                <circle cx="172" cy="172" r="64.5" fill="#D19049"></circle>
              </g>
              <defs>
                <filter id="filter0_f_644_12578" x="0.887032" y="0.887032" width="342.226" height="342.226" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                  <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
                  <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feBlend>
                  <feGaussianBlur stdDeviation="53.5565" result="effect1_foregroundBlur_644_12578"></feGaussianBlur>
                </filter>
              </defs>
            </svg>
          </div>
        </div>

        {/* Content with relative positioning to appear above blur effects */}
        <div className="relative z-10">
          {children}
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default PageLayout;
