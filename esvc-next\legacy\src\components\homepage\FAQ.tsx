import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import '../../styles/components/homepage/FAQ.css';

interface FAQItem {
  id: number;
  question: string;
  answer: string;
}

const FAQ: React.FC = () => {
  const navigate = useNavigate();
  const [openItem, setOpenItem] = useState<number | null>(2); // Second item open by default

  const handleStartStaking = () => {
    // Navigate to My Stake page in user dashboard
    navigate('/user-dashboard/my-stake');
  };

  const handleSeeHowItWorks = () => {
    // Navigate to login page to see how it works
    navigate('/login');
  };

  const faqItems: FAQItem[] = [
    {
      id: 1,
      question: "What is ESVC?",
      answer: "ESVC is a revolutionary token-gated startup funding platform that allows you to stake tokens securely, earn daily ROI, and unlock exclusive opportunities to pitch your startup ideas for funding."
    },
    {
      id: 2,
      question: "How do I stake ESVC?",
      answer: "Simply click 'Start Staking Now,' create an account, choose how much you want to stake (in USD), and deposit using either Solana (SOL) or USDC. Your ESVC tokens will be automatically purchased and staked for a 6 or 12-month period."
    },
    {
      id: 3,
      question: "What's the minimum amount I can stake?",
      answer: "The minimum staking amount varies depending on the current market conditions and staking tier. Please check our staking dashboard for the most up-to-date minimum requirements."
    },
    {
      id: 4,
      question: "How much ROI can I earn?",
      answer: "ESVC staking offers competitive annual returns of up to 20%. The exact ROI depends on various factors including staking duration, market conditions, and treasury performance."
    },
    {
      id: 5,
      question: "When can I withdraw my ROI?",
      answer: "ROI is distributed daily to stakers. You can withdraw your earned rewards at any time, while your principal stake remains locked for the chosen staking period (6 or 12 months)."
    }
  ];

  const toggleItem = (id: number) => {
    setOpenItem(openItem === id ? null : id);
  };

  return (
    <section className="faq">
      <div className="container">
        <div className="faq-header">
          <h2 className="faq-title">Frequently Asked Questions</h2>
        </div>

        <div className="faq-content">
          <div className="faq-list">
            {faqItems.map((item) => (
              <div 
                key={item.id} 
                className={`faq-item ${openItem === item.id ? 'open' : ''}`}
              >
                <button 
                  className="faq-question"
                  onClick={() => toggleItem(item.id)}
                >
                  <span className="question-number">{item.id}.</span>
                  <span className="question-text">{item.question}</span>
                  <span className="question-icon">
                    {openItem === item.id ? '−' : '+'}
                  </span>
                </button>
                
                {openItem === item.id && (
                  <div className="faq-answer">
                    <p>{item.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="faq-actions">
          <button className="btn-primary" onClick={handleStartStaking}>
            Start Staking Now
            <span className="cta-icon">🚀</span>
          </button>
          <button className="btn-secondary" onClick={handleSeeHowItWorks}>
            See How It Works
            <span className="arrow">→</span>
          </button>
        </div>
      </div>
    </section>
  );
};

export default FAQ;
