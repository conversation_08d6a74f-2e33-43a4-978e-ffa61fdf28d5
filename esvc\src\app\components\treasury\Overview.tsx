'use client';

import React from 'react';

const Overview: React.FC = () => {
  // Dashboard cards data matching the design
  const dashboardCards = [
    {
      title: 'TOTAL AMOUNT OF ESVC STAKED',
      value: '177,874,389.00',
      unit: 'ESVC',
      change: '+ 4.8% Today',
      changeType: 'positive'
    },
    {
      title: 'FUNDS AVAILABLE FOR DAILY ROI PAYOUTS',
      value: '$609,185',
      change: '+ 4.8% Today',
      changeType: 'positive'
    },
    {
      title: 'FUNDS AVAILABLE TO FUND STARTUPS',
      value: '$43,700',
      change: '+ 4.8% Today',
      changeType: 'positive',
      hasInfoIcon: true
    },
    {
      title: 'TOTAL NUMBER OF STAKERS',
      value: '11,302',
      change: '+ 4.8% Today',
      changeType: 'positive'
    },
    {
      title: 'TOTAL ESVC SOLD TO DATE',
      value: '$29,400,200.00',
      change: '+ 4.8% Today',
      changeType: 'positive'
    },
    {
      title: 'TOTAL PROFIT GENERATED',
      value: '$43,700',
      change: '+ 4.8% Today',
      changeType: 'positive'
    }
  ];

  return (
    <div className="flex-1 max-w-[920px]">
      {/* Section Header */}
      <div className="mb-6">
        <h2 className="text-3xl font-semibold text-white m-0 font-montserrat">Overview</h2>
      </div>

      {/* Dashboard Cards Grid - 2 columns on desktop, 1 on mobile */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 max-w-[800px]">
        {dashboardCards.map((card, index) => (
          <div
            key={index}
            className="bg-[#262626] border border-[rgba(255,255,255,0.1)] rounded-2xl p-6 flex flex-col gap-4 transition-all duration-300 hover:border-[rgba(191,65,41,0.3)]"
          >
            {/* Card Header */}
            <div className="flex items-start justify-between">
              <h3 className="font-montserrat text-xs font-semibold text-[#999999] uppercase tracking-wide leading-4 flex items-center gap-2">
                {card.title}
                {card.hasInfoIcon && (
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" className="text-[#999999]">
                    <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                    <path d="m9,9 0,0 a3,3 0 1,1 6,0c0,2 -3,3 -3,3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="m12,17 .01,0" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                )}
              </h3>
            </div>

            {/* Card Value */}
            <div className="flex items-baseline gap-2">
              <span className="font-montserrat text-3xl font-bold text-white">
                {card.value}
              </span>
              {card.unit && (
                <span className="text-lg font-medium text-[#CCCCCC]">
                  {card.unit}
                </span>
              )}
            </div>

            {/* Card Change */}
            <div className={`text-sm font-medium flex items-center gap-2 ${
              card.changeType === 'positive' ? 'text-[#4CAF50]' : 'text-[#FF6B6B]'
            }`}>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" className="text-current">
                <path d="m3 17 6-6 4 4 8-8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="m21 7-4 2-2-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              {card.change}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Overview;
