/* Trade Challenge Content */
.trade-challenge-content {
  position: relative;
  z-index: 2;
  padding: 10px 0 80px; /* Further reduced top padding to move content up */
}

/* Hero Section */
.hero-section {
  text-align: center;
  padding: 0 40px 60px; /* Reduced bottom padding */
  max-width: 1000px;
  margin: 0 auto;
}

.title-section {
  margin-bottom: 32px;
}

.hero-title {
  font-size: 48px;
  font-weight: 700;
  margin: 0 0 16px 0;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.amount {
  color: #BF4129;
}

.arrow-icon {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.target {
  color: #FFFFFF;
}

.group-line {
  width: 100%;
  max-width: 600px;
  height: auto;
  margin: -50px auto 0 auto; /* Position directly under title */
  display: block;
}

/* Arrow visibility controls */
.desktop-arrow {
  display: inline-block;
}

.mobile-arrow {
  display: none;
}

.hero-subtitle {
  font-size: 18px;
  color: #CCCCCC;
  line-height: 28px;
  margin: 0 0 40px 0;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.get-bot-btn {
  background: linear-gradient(135deg, #F0C369 0%, #C6741B 100%);
  color: #1A1A1A;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
}

.get-bot-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(240, 195, 105, 0.3);
}

/* Join Challenge Section */
.join-challenge-section {
  text-align: center;
  margin-bottom: 80px;
  padding: 40px 20px;
  background: transparent;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.join-challenge-title {
  font-size: 32px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 40px 0;
}

/* Go Back Button */
.go-back-btn {
  background: transparent;
  border: none;
  color: #F0C369;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 24px;
  cursor: pointer;
  transition: color 0.3s ease;
}

.go-back-btn:hover {
  color: #FFFFFF;
}

.go-back-btn span {
  font-size: 16px;
}

/* Challenge Steps - Progress Bar Style */
.challenge-steps {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 60px;
  position: relative;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Progress line connecting the steps */
.challenge-steps::before {
  content: '';
  position: absolute;
  top: 20px;
  left: 15%;
  right: 15%;
  height: 4px;
  background: #404040;
  z-index: 1;
}

/* Active progress line - Step 1 */
.challenge-steps::after {
  content: '';
  position: absolute;
  top: 20px;
  left: 15%;
  width: 35%;
  height: 4px;
  background: #F0C369;
  z-index: 2;
  transition: width 0.3s ease, background 0.3s ease;
}

/* Progress line for completed step 1 and active step 2 */
.challenge-steps.step-2::after {
  width: 50%;
  background: #F0C369;
}

/* Progress line for completed steps 1-2 */
.challenge-steps.step-1-completed::after {
  background: #22C55E;
}

.challenge-steps.step-2-active::after {
  width: 50%;
  background: #F0C369;
}

.challenge-steps.step-2-completed::after {
  width: 85%;
  background: #22C55E;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  flex: 1;
  position: relative;
  z-index: 3;
}

.step-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #404040;
  color: #CCCCCC;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 16px;
  border: 3px solid #404040;
}

.step-item.active .step-circle {
  background: #F0C369;
  color: #1A1A1A;
  border-color: #F0C369;
}

.step-item.completed .step-circle {
  background: #22C55E;
  color: #FFFFFF;
  border-color: #22C55E;
}

.step-label {
  font-size: 12px;
  font-weight: 600;
  color: #666666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
  max-width: 120px;
}

.step-item.active .step-label {
  color: #F0C369;
}

.step-item.completed .step-label {
  color: #22C55E;
}

/* Payment Section */
.payment-section {
  max-width: 500px;
  margin: 0 auto;
}

.payment-title {
  font-size: 24px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 16px 0;
}

.payment-subtitle {
  font-size: 16px;
  color: #CCCCCC;
  line-height: 1.5;
  margin: 0 0 32px 0;
}

.payment-form {
  text-align: left;
}

.payment-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #CCCCCC;
  margin-bottom: 8px;
}

.payment-select {
  width: 100%;
  background: #262626;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 12px 16px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #FFFFFF;
  cursor: pointer;
}

.payment-select:focus {
  outline: none;
  border-color: #F0C369;
}

/* Currency Display */
.currency-display {
  background: #333333;
  border: 1px solid #555555;
  border-radius: 8px;
  padding: 12px 16px;
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 500;
}

.selected-currency {
  color: #F0C369;
}

/* QR Payment Container */
.qr-payment-container {
  background: #1A1A1A;
  border: 1px solid #333333;
  border-radius: 16px;
  padding: 32px;
  margin-top: 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.qr-code-section {
  background: #FFFFFF;
  padding: 16px;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.qr-canvas {
  display: block;
}

.wallet-address-section {
  width: 100%;
  text-align: center;
}

.wallet-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #CCCCCC;
  margin-bottom: 8px;
}

.wallet-address-display {
  background: #262626;
  border: 1px solid #404040;
  border-radius: 8px;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.wallet-address {
  color: #FFFFFF;
  font-size: 14px;
  font-family: monospace;
  flex: 1;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
}

.copy-btn {
  background: transparent;
  border: none;
  color: #F0C369;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.copy-btn:hover {
  color: #FFFFFF;
}

.payment-timer {
  text-align: center;
}

.timer-display {
  background: #F0C369;
  color: #1A1A1A;
  font-size: 24px;
  font-weight: 700;
  padding: 12px 24px;
  border-radius: 8px;
  font-family: monospace;
}

.payment-info {
  background: #0C1D38;
  border: 1px solid #1E3A8A;
  border-radius: 8px;
  padding: 16px;
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
}

.info-icon {
  color: #3B82F6;
  font-size: 16px;
  font-weight: bold;
}

.info-text {
  color: #CCCCCC;
  font-size: 14px;
  margin: 0;
}

.deposit-complete-btn {
  background: #F0C369;
  color: #1A1A1A;
  border: none;
  border-radius: 8px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
  width: 100%;
}

.deposit-complete-btn:hover {
  background: #E5B85C;
}

/* Payment Success Section */
.payment-success-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 40px 20px;
}

.success-card {
  /* Modal styling from Figma */
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
  gap: 24px;

  position: relative;
  width: 560px;
  height: 462px;

  background: #262626;
  border-radius: 24px;

  /* Center the card */
  margin: 0 auto;
}

.success-icon {
  margin-bottom: 8px;
}

.checkmark-circle {
  width: 80px;
  height: 80px;
  background: #22C55E;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.checkmark {
  color: white;
  font-size: 32px;
  font-weight: bold;
}

.success-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 600;
  color: white;
  line-height: 1.4;
  margin: 0;
  text-align: center;
}

.success-subtitle {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #A3A3A3;
  line-height: 1.5;
  margin: 0;
  text-align: center;
}

.proceed-btn {
  background: #BF4129;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.proceed-btn:hover {
  background: #A63621;
  transform: translateY(-1px);
}

/* Link Account Section */
.link-account-section {
  max-width: 600px;
  margin: 0 auto;
}

.link-title {
  font-size: 24px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 16px 0;
  text-align: center;
}

.link-subtitle {
  font-size: 16px;
  color: #CCCCCC;
  line-height: 1.5;
  margin: 0 0 32px 0;
  text-align: center;
}

.api-info {
  background: #0C1D38;
  border: 1px solid #1E3A8A;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 24px;
}

.api-info .info-icon {
  color: #3B82F6;
  font-size: 16px;
  font-weight: bold;
  margin-top: 2px;
}

.api-info .info-text {
  color: #CCCCCC;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.api-tutorial {
  text-align: center;
  margin-bottom: 32px;
}

.tutorial-btn {
  background: transparent;
  border: 1px solid #F0C369;
  border-radius: 8px;
  color: #F0C369;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.tutorial-btn:hover {
  background: #F0C369;
  color: #1A1A1A;
}

.tutorial-icon {
  font-size: 12px;
}

.exchange-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.exchange-btn {
  background: #262626;
  border: 1px solid #404040;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.exchange-btn:hover {
  border-color: #F0C369;
  background: #2A2A2A;
}

.exchange-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  color: #FFFFFF;
}

.coinbase-btn .exchange-icon {
  background: #0052FF;
}

.binance-btn .exchange-icon {
  background: #F0B90B;
  color: #000000;
}

.exchange-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.exchange-name {
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 600;
}

.exchange-desc {
  color: #CCCCCC;
  font-size: 14px;
}

.link-text {
  color: #F0C369;
  font-size: 14px;
  font-weight: 500;
}

/* Modal Styles */
.modal-blur-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.blurred {
  filter: blur(4px);
  pointer-events: none;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100;
  padding: 20px;
}

.modal-content {
  background: #2A2A2A;
  border: 1px solid #404040;
  border-radius: 16px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

/* Success Modal Specific Styles */
.success-modal {
  text-align: center;
  padding: 40px 32px;
  max-width: 400px;
  background: #2A2A2A;
}

.success-modal .success-icon {
  margin-bottom: 24px;
}

.success-checkmark {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: #08210e !important;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: bold;
  margin: 0 auto;
  box-shadow: 0 4px 12px rgba(8, 33, 14, 0.3);
}

.success-modal .success-title {
  color: #FFFFFF;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 16px 0;
  line-height: 1.3;
}

.success-modal .success-subtitle {
  color: #CCCCCC;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 32px 0;
}

.success-modal .proceed-btn,
.success-modal .dashboard-btn {
  background: linear-gradient(135deg, #F0C369 0%, #C6741B 100%);
  color: #1A1A1A;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  box-shadow: 0 4px 12px rgba(240, 195, 105, 0.3);
}

.success-modal .proceed-btn:hover,
.success-modal .dashboard-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(240, 195, 105, 0.4);
}

.modal-header {
  padding: 24px 24px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  color: #FFFFFF;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.modal-close {
  background: transparent;
  border: none;
  color: #CCCCCC;
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  line-height: 1;
  transition: color 0.3s ease;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  color: #FFFFFF;
}

.modal-body {
  padding: 24px;
}

.modal-subtitle {
  color: #CCCCCC;
  font-size: 13px;
  line-height: 1.5;
  margin: 0 0 24px 0;
}

.api-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  color: #FFFFFF;
  font-size: 14px;
  font-weight: 500;
}

.form-input {
  background: #1A1A1A;
  border: 1px solid #404040;
  border-radius: 8px;
  padding: 12px 16px;
  color: #FFFFFF;
  font-size: 14px;
  transition: border-color 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #F0C369;
}

.form-input::placeholder {
  color: #666666;
}

.form-info {
  background: #0C1D38;
  border: 1px solid #1E3A8A;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.form-info .info-icon {
  color: #3B82F6;
  font-size: 16px;
  font-weight: bold;
  margin-top: 2px;
}

.form-info .info-text {
  color: #CCCCCC;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.connect-btn {
  background: linear-gradient(135deg, #F0C369 0%, #C6741B 100%);
  color: #1A1A1A;
  border: none;
  border-radius: 12px;
  padding: 16px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  box-shadow: 0 4px 12px rgba(240, 195, 105, 0.3);
}

.connect-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(240, 195, 105, 0.4);
}

/* Main Content */
.main-content {
  width: 100%;
  margin: 0;
  padding: 20px 40px 0; /* Reduced top padding to move content up */
  background: #281705;
}

.main-content > * {
  max-width: 1200px;
  margin: 0 auto;
}

.content-header {
  text-align: center;
  margin-bottom: 60px;
}

.content-title {
  font-size: 32px;
  font-weight: 600;
  color: #F0C369;
  margin: 0 0 16px 0;
}

.content-subtitle {
  font-size: 18px;
  color: #CCCCCC;
  line-height: 28px;
  margin: 0;
}

/* Trading Dashboard */
.trading-dashboard {
  display: grid;
  grid-template-columns: 5fr !important; /* Make balance trend card wider */
  gap: 40px;
  margin-bottom: 60px;
}

/* Live Tracker */
.live-tracker {
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 32px;
}

.tracker-title {
  font-size: 20px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 24px 0;
}

.tracker-stats {
  display: flex;
  flex-direction: column;
  gap: 0; /* Remove gap since we're using padding and borders */
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.stat-icon img {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.stat-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #CCCCCC;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 18px;
  color: #FFFFFF;
  font-weight: 700;
}

/* Balance Trend Chart */
.balance-trend {
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 32px;
}

.chart-title {
  font-size: 20px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 24px 0;
}

.chart-container {
  height: 300px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Trading Options */
.trading-options {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 80px; /* Extended spacing to cover button better */
  flex-wrap: wrap;
}

.option-btn {
  background: rgba(26, 26, 26, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  padding: 16px 24px;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.option-btn:hover {
  border-color: #F0C369;
  color: #FFFFFF;
}

.option-btn.active {
  background: rgba(240, 195, 105, 0.1);
  border-color: #F0C369;
  color: #F0C369;
}

.option-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.option-icon img {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

/* Bottom CTA */
.bottom-cta {
  text-align: center;
}

.get-bot-btn-bottom {
  background: linear-gradient(135deg, #F0C369 0%, #C6741B 100%);
  color: #1A1A1A;
  border: none;
  border-radius: 12px;
  padding: 20px 40px;
  font-family: 'Montserrat', sans-serif;
  font-size: 18px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
}

.get-bot-btn-bottom:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(240, 195, 105, 0.3);
}

/* How it Works Section */
.how-it-works {
  padding: 80px 40px;
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: start;
}

.how-it-works-left {
  display: flex;
  flex-direction: column;
}

.how-it-works-title {
  font-size: 32px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 16px;
}

.how-it-works-subtitle {
  font-size: 16px;
  color: #CCCCCC;
  margin: 0 0 40px 0;
  line-height: 24px;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Removed conflicting card styling for step-item to maintain horizontal progress bar */

.step-number {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: 600;
  color: #F0C369;
  min-width: 40px;
  text-align: center;
}

.step-text {
  font-size: 16px;
  color: #FFFFFF;
  font-weight: 500;
}

.how-it-works-right {
  display: flex;
  flex-direction: column;
}

.backed-section {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 40px;
}

.backed-title {
  font-size: 24px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 8px 0;
}

.backed-subtitle {
  font-size: 16px;
  color: #BF4129;
  margin: 0 0 24px 0;
  font-weight: 600;
}

.backed-list {
  list-style: none;
  padding: 0;
  margin: 0 0 24px 0;
}

.backed-list li {
  font-size: 14px;
  color: #CCCCCC;
  margin-bottom: 8px;
  padding-left: 16px;
  position: relative;
  line-height: 20px;
}

.backed-list li::before {
  content: "•";
  color: #F0C369;
  position: absolute;
  left: 0;
}

.treasury-link {
  color: #F0C369;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.treasury-link:hover {
  text-decoration: underline;
}

.join-movement {
  text-align: center;
  margin-top: 40px;
}

.join-movement-text {
  font-size: 16px;
  color: #CCCCCC;
  margin: 0 0 24px 0;
}

.get-bot-btn-section {
  background: linear-gradient(135deg, #F0C369 0%, #C6741B 100%);
  color: #1A1A1A;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
}

.get-bot-btn-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(240, 195, 105, 0.3);
}

/* Connection Success Step */
.connection-success-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 40px 20px;
}

.connection-success-card {
  background: #404040;
  border-radius: 24px;
  padding: 48px;
  text-align: center;
  max-width: 560px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.success-icon {
  margin-bottom: 8px;
}

.checkmark-circle {
  width: 80px;
  height: 80px;
  background: #22C55E;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.checkmark {
  color: white;
  font-size: 32px;
  font-weight: bold;
}

.success-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 600;
  color: white;
  line-height: 1.4;
  margin: 0;
  text-align: center;
}

.success-subtitle {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #A3A3A3;
  line-height: 1.5;
  margin: 0;
  text-align: center;
}

.dashboard-btn {
  background: #BF4129;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.dashboard-btn:hover {
  background: #A63621;
  transform: translateY(-1px);
}

/* More Than Trading Section */
.more-than-trading {
  background: rgba(0, 0, 0, 0.6);
  padding: 80px 40px;
  text-align: center;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.more-than-trading-content {
  max-width: 800px;
  margin: 0 auto;
}

.more-than-trading-title {
  font-size: 32px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 24px 0;
}

.more-than-trading-text {
  font-size: 16px;
  color: #CCCCCC;
  line-height: 24px;
  margin: 0 0 40px 0;
}

.more-than-trading-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.start-staking-btn {
  background: #BF4129;
  color: #FFFFFF;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.start-staking-btn:hover {
  background: #A03622;
  transform: translateY(-2px);
}

.get-funded-btn {
  background: transparent;
  color: #FFFFFF;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 16px 32px;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.get-funded-btn:hover {
  border-color: #F0C369;
  color: #F0C369;
  transform: translateY(-2px);
}

/* Dashboard Success Alert */
.dashboard-success-alert {
  background: #155724;
  color: #19C37D;
  border-radius: 10px;
  padding: 18px 24px;
  margin: 32px 0 24px 0;
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 1rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.dashboard-success-alert span {
  display: inline-block;
  background: #19C37D;
  color: #fff;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  text-align: center;
  line-height: 28px;
  margin-right: 16px;
  font-weight: 700;
  font-size: 1.2rem;
}

/* Dashboard Title */
.dashboard-title {
  color: #fff;
  text-align: center;
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 32px;
  text-decoration: underline;
  text-decoration-color: #3B82F6;
  text-underline-offset: 8px;
  letter-spacing: 0.5px;
  display: block;
}

/* Dashboard Cards Grid */
.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  max-width: 780px;
  margin: 0 auto 32px auto;
  justify-items: center;
  align-items: center;
}

/* Dashboard Card */
.dashboard-card {
  background: rgba(38, 38, 38, 0.6); /* Transparent background like other dashboards */
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 0 20px;
  color: #fff;
  min-width: 220px;
  width: 220px;
  height: 160px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  text-align: left;
}

.dashboard-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(191, 65, 41, 0.3);
}

.dashboard-card > div:first-child {
  font-size: 12px; /* Smaller, consistent with other dashboards */
  margin-bottom: 12px;
  color: #999999; /* Lighter color for better contrast */
  font-weight: 600;
  letter-spacing: 1px;
  text-transform: uppercase;
}

.dashboard-card > div:nth-child(2) {
  font-size: 32px; /* Consistent with other dashboards */
  font-weight: 700;
  margin-bottom: 12px;
  color: #fff;
}

.dashboard-card .dashboard-profit,
.dashboard-card .dashboard-growth {
  color: #19C37D;
  margin-top: 8px;
  font-size: 0.95rem;
  font-weight: 500;
}

.dashboard-card .dashboard-loss {
  color: #F87171;
  margin-top: 8px;
  font-size: 0.95rem;
  font-weight: 500;
}

/* Responsive for Dashboard */
@media (max-width: 900px) {
  .dashboard-cards {
    grid-template-columns: repeat(2, 1fr);
    max-width: 500px;
  }
}

@media (max-width: 600px) {
  .dashboard-cards {
    grid-template-columns: 1fr;
    max-width: 95vw;
    gap: 16px; /* Increased gap for better spacing */
    padding: 0 20px; /* Add horizontal padding */
  }
  .dashboard-card {
    min-width: 0;
    width: 100%;
    height: 120px;
    padding: 16px 20px; /* Better padding for mobile */
    border-radius: 12px; /* Smaller border radius for mobile */
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .trade-challenge-content {
    padding: 20px 0 40px; /* Further reduced padding for mobile */
  }

  .hero-section {
    padding: 0 20px 40px;
  }

  .title-section {
    margin-bottom: 16px;
  }

  .hero-title {
    font-size: 28px;
    flex-direction: column;
    gap: 8px;
    text-align: center;
    margin-bottom: 12px;
  }

  /* Mobile arrow controls */
  .desktop-arrow {
    display: none;
  }

  .mobile-arrow {
    display: inline-block;
  }

  .arrow-icon {
    width: 28px;
    height: 28px;
  }

  .group-line {
    max-width: 400px;
    margin: 16px auto 0 auto; /* Position directly under "1 Billion in 3,000 Trades" on mobile */
  }

  .hero-subtitle {
    font-size: 16px;
  }

  .main-content {
    padding: 0 20px;
  }

  .content-header {
    margin-bottom: 60px;
    padding-top: 60px; /* Add space to the top on mobile */
  }

  .content-title {
    font-size: 24px;
  }

  .content-subtitle {
    font-size: 16px;
  }

  .trading-dashboard {
    grid-template-columns: 1fr;
    gap: 24px;
    margin-bottom: 40px;
  }

  .live-tracker,
  .balance-trend {
    padding: 24px;
  }

  .tracker-stats {
    flex-direction: column;
    gap: 0; /* Remove gap since we're using padding and borders */
  }

  .tracker-title,
  .chart-title {
    font-size: 18px;
  }

  .stat-item {
    gap: 12px;
  }

  /* Join Challenge Section - Mobile */
  .join-challenge-section {
    margin-bottom: 24px;
    padding: 16px;
    background: transparent;
  }

  .join-challenge-title {
    font-size: 22px;
    margin-bottom: 20px;
  }

  .challenge-steps {
    margin-bottom: 20px;
    max-width: 100%;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  /* Hide progress lines on mobile */
  .challenge-steps::before,
  .challenge-steps::after {
    display: none;
  }

  .step-item {
    flex-direction: row;
    align-items: center;
    gap: 12px;
    width: 100%;
    justify-content: flex-start;
  }

  .step-circle {
    width: 32px;
    height: 32px;
    font-size: 14px;
    flex-shrink: 0;
  }

  .step-label {
    font-size: 14px;
    max-width: none;
    text-align: left;
    font-weight: 500;
  }

  .payment-title {
    font-size: 18px;
  }

  .payment-subtitle {
    font-size: 14px;
    margin-bottom: 16px;
  }

  .stat-icon {
    width: 32px;
    height: 32px;
    font-size: 18px;
  }

  .stat-value {
    font-size: 16px;
  }

  .chart-placeholder {
    height: 200px;
  }

  .trading-options {
    flex-direction: column;
    align-items: center;
    gap: 12px;
    margin-bottom: 40px; /* Reduced spacing */
  }

  .option-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .bottom-cta {
    padding: 20px 0; /* Reduced space around button on mobile */
  }

  .get-bot-btn-bottom {
    padding: 16px 32px;
    font-size: 16px;
  }

  /* How it Works Mobile */
  .how-it-works {
    grid-template-columns: 1fr;
    gap: 40px;
    padding: 60px 20px;
    text-align: center; /* Center content on mobile */
  }

  .how-it-works-title {
    font-size: 24px;
    justify-content: center; /* Center the title and decoration on mobile */
  }

  .how-it-works-subtitle {
    font-size: 14px;
  }

  /* Removed conflicting mobile padding for step-item to maintain horizontal progress bar */

  .step-text {
    font-size: 14px;
  }

  .backed-section {
    padding: 24px;
    margin-bottom: 24px;
  }

  .backed-title {
    font-size: 20px;
  }

  .backed-subtitle {
    font-size: 14px;
  }

  .backed-list li {
    font-size: 13px;
  }

  .treasury-link {
    font-size: 13px;
  }

  .join-movement {
    margin-top: 24px;
  }

  .join-movement-text {
    font-size: 14px;
  }

  .get-bot-btn-section {
    padding: 14px 28px;
    font-size: 14px;
  }

  /* More Than Trading Mobile */
  .more-than-trading {
    padding: 60px 20px;
  }

  .more-than-trading-title {
    font-size: 24px;
  }

  .more-than-trading-text {
    font-size: 14px;
  }

  .more-than-trading-buttons {
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .start-staking-btn,
  .get-funded-btn {
    width: 100%;
    max-width: 300px;
    padding: 14px 28px;
    font-size: 14px;
    justify-content: center;
  }

  /* Mobile QR Payment */
  .qr-payment-container {
    padding: 24px 16px;
    margin-top: 24px;
  }

  .wallet-address {
    font-size: 12px;
  }

  .timer-display {
    font-size: 20px;
    padding: 10px 20px;
  }

  /* Mobile Success Card */
  .payment-success-section {
    min-height: 50vh;
    padding: 20px;
  }

  .success-card {
    width: 100%;
    max-width: 400px;
    height: auto;
    padding: 32px 24px;
    gap: 20px;
  }

  .checkmark-circle {
    width: 64px;
    height: 64px;
  }

  .checkmark {
    font-size: 24px;
  }

  .success-title {
    font-size: 20px;
  }

  .success-subtitle {
    font-size: 14px;
  }

  .proceed-btn {
    padding: 14px 28px;
    font-size: 14px;
  }

  /* Mobile Link Account */
  .link-title {
    font-size: 20px;
  }

  .link-subtitle {
    font-size: 14px;
  }

  .exchange-btn {
    padding: 16px;
  }

  .exchange-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .exchange-name {
    font-size: 14px;
  }

  .exchange-desc {
    font-size: 12px;
  }

  /* Mobile Modal */
  .modal-blur-bg {
    padding: 12px;
  }

  .modal-overlay {
    padding: 12px; /* Reduced padding */
  }

  .modal-content {
    max-height: 80vh; /* Reduced max height */
    border-radius: 12px; /* Smaller border radius */
  }

  /* Mobile Success Modal */
  .success-modal {
    padding: 32px 20px;
    max-width: 100%;
  }

  .success-checkmark {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }

  .success-modal .success-title {
    font-size: 18px;
    margin-bottom: 12px;
  }

  .success-modal .success-subtitle {
    font-size: 13px;
    margin-bottom: 24px;
  }

  .modal-title {
    font-size: 16px;
  }

  .modal-subtitle {
    font-size: 12px;
  }

  .form-label {
    font-size: 13px;
  }

  .form-input {
    padding: 12px 14px;
    font-size: 14px;
  }

  .modal-header {
    padding: 16px 16px 0; /* Reduced padding */
  }

  .modal-title {
    font-size: 16px; /* Smaller title */
  }

  .modal-body {
    padding: 16px; /* Reduced padding */
  }

  .modal-subtitle {
    font-size: 12px; /* Smaller subtitle */
    line-height: 1.4; /* Tighter line height */
  }

  .form-input {
    padding: 8px 12px; /* Reduced padding */
    font-size: 13px; /* Smaller font */
  }

  /* Mobile Progress Bar Updates - Not needed since we hide progress lines on mobile */

  /* Connection Success Mobile */
  .connection-success-container {
    min-height: 50vh;
    padding: 20px;
  }

  .connection-success-card {
    padding: 32px 24px;
    gap: 20px;
  }

  .checkmark-circle {
    width: 64px;
    height: 64px;
  }

  .checkmark {
    font-size: 24px;
  }

  .success-title {
    font-size: 20px;
  }

  .success-subtitle {
    font-size: 14px;
  }

  .dashboard-btn {
    padding: 14px 28px;
    font-size: 14px;
  }
}
