import React from 'react';
import DashboardLayout from './DashboardLayout';
import '../styles/components/TradingDashboardMain.css';

interface TradingDashboardMainProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
  onGoBack?: () => void;
}

const TradingDashboardMain: React.FC<TradingDashboardMainProps> = ({ onGoBack }) => {
  return (
    <DashboardLayout className="trading-dashboard-main-container">
      <div className="trading-dashboard-main-content">
        {/* Go Back Button */}
        {onGoBack && (
          <button className="go-back-btn" onClick={onGoBack}>
            <span>←</span> Go Back
          </button>
        )}

        {/* Header Section */}
        <div className="trading-dashboard-header">
          <h1 className="trading-dashboard-title">ESVC Trade Challenge</h1>
        </div>

        {/* Success Alert */}
        <div className="dashboard-success-alert">
          <div className="success-icon">✓</div>
          <div className="success-content">
            <div className="success-title">You've Successfully Joined the Trade Challenge!</div>
            <div className="success-description">
              We'll begin trading once your exchange is connected and capital is funded. You'll be notified of trade activities and performance in your dashboard.
            </div>
          </div>
        </div>

        {/* Dashboard Title */}
        <h2 className="dashboard-section-title">Your Trading Dashboard</h2>

        {/* Dashboard Cards */}
        <div className="trading-dashboard-cards">
          <div className="trading-dashboard-card">
            <div className="card-label">TOTAL BALANCE</div>
            <div className="card-value">$6,500</div>
            <div className="card-change positive">
              <span className="change-icon">📈</span>
              +4.8% Today
            </div>
          </div>
          
          <div className="trading-dashboard-card">
            <div className="card-label">PENDING IN TRADE</div>
            <div className="card-value">$3,700</div>
            <div className="card-change positive">
              <span className="change-icon">📈</span>
              +4.8% Today
            </div>
          </div>
          
          <div className="trading-dashboard-card">
            <div className="card-label">CAPITAL</div>
            <div className="card-value">$2,185</div>
          </div>
          
          <div className="trading-dashboard-card">
            <div className="card-label">PROFIT</div>
            <div className="card-value">$2,185</div>
            <div className="card-change positive">
              <span className="change-icon">📈</span>
              +4.8% Today
            </div>
          </div>
          
          <div className="trading-dashboard-card">
            <div className="card-label">LOSS</div>
            <div className="card-value">$185</div>
            <div className="card-change negative">
              <span className="change-icon">📉</span>
              -0.6% Today
            </div>
          </div>
          
          <div className="trading-dashboard-card">
            <div className="card-label">PROFIT WITHDRAWN</div>
            <div className="card-value">$2,185</div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default TradingDashboardMain;
