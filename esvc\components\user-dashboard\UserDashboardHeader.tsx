import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import '../../styles/components/user-dashboard/UserDashboardHeader.css';
import logoEsvc from '../../assets/logo-esvc.png';

interface UserDashboardHeaderProps {
  onNavigateToLanding?: () => void;
}

const UserDashboardHeader: React.FC<UserDashboardHeaderProps> = ({
  onNavigateToLanding
}) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isProfileDropdownOpen, setIsProfileDropdownOpen] = useState(false);
  const navigate = useNavigate();
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const handleMenuToggleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('User Dashboard Menu toggle clicked, current state:', isMobileMenuOpen);
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const toggleProfileDropdown = () => {
    setIsProfileDropdownOpen(!isProfileDropdownOpen);
  };

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target as Node)) {
        closeMobileMenu();
      }
    };

    if (isMobileMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMobileMenuOpen]);

  return (
    <header className="user-dashboard-header">
      <div className="container">
        <div className="header-content">
          {/* Logo */}
          <div className="logo" onClick={onNavigateToLanding} style={{ cursor: 'pointer' }}>
            <img src={logoEsvc} alt="ESVC Logo" className="logo-image" />
          </div>

          {/* Desktop Navigation */}
          <nav className="nav desktop-nav">
            <button onClick={onNavigateToLanding} className="nav-link active">Home</button>
            <button onClick={() => navigate('/stake-esvc')} className="nav-link">Stake ESVC</button>
            <button onClick={() => navigate('/user-dashboard/get-funding')} className="nav-link">Get Funding</button>
            <button onClick={() => navigate('/trade-challenge')} className="nav-link">Trade Challenge</button>
            <button onClick={() => navigate('/contact-us')} className="nav-link">Contact Us</button>
          </nav>

          {/* Desktop User Profile */}
          <div className="header-actions desktop-actions">
            <div className="user-profile-dropdown">
              <button 
                className="profile-button"
                onClick={toggleProfileDropdown}
              >
                <div className="profile-avatar">
                  <span className="avatar-text">AO</span>
                </div>
                <svg 
                  className={`dropdown-arrow ${isProfileDropdownOpen ? 'open' : ''}`}
                  width="12" 
                  height="8" 
                  viewBox="0 0 12 8" 
                  fill="none"
                >
                  <path 
                    d="M1 1.5L6 6.5L11 1.5" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                  />
                </svg>
              </button>
              
              {isProfileDropdownOpen && (
                <div className="profile-dropdown-menu">
                  <div className="dropdown-item">
                    <span>Profile Settings</span>
                  </div>
                  <div className="dropdown-item">
                    <span>Security</span>
                  </div>
                  <div className="dropdown-item">
                    <span>Notifications</span>
                  </div>
                  <div className="dropdown-divider"></div>
                  <div className="dropdown-item logout">
                    <span>Logout</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Mobile Hamburger Menu */}
          <button
            className={`mobile-menu-toggle ${isMobileMenuOpen ? 'menu-open' : ''}`}
            onClick={handleMenuToggleClick}
            type="button"
            aria-label={isMobileMenuOpen ? 'Close menu' : 'Open menu'}
          >
            <span className="hamburger-line"></span>
            <span className="hamburger-line"></span>
            <span className="hamburger-line"></span>
          </button>
        </div>

        {/* Mobile Menu */}
        <div ref={mobileMenuRef} className={`mobile-menu ${isMobileMenuOpen ? 'mobile-menu-open' : ''}`}>
          <nav className="mobile-nav">
            <button onClick={() => { onNavigateToLanding?.(); closeMobileMenu(); }} className="mobile-nav-link">Home</button>
            <button onClick={() => { navigate('/stake-esvc'); closeMobileMenu(); }} className="mobile-nav-link">Stake ESVC</button>
            <button onClick={() => { navigate('/user-dashboard/get-funding'); closeMobileMenu(); }} className="mobile-nav-link">Get Funding</button>
            <button onClick={() => { navigate('/trade-challenge'); closeMobileMenu(); }} className="mobile-nav-link">Trade Challenge</button>
            <button onClick={() => { navigate('/contact-us'); closeMobileMenu(); }} className="mobile-nav-link">Contact Us</button>
          </nav>
          <div className="mobile-actions">
            <div className="mobile-user-profile">
              <div className="profile-avatar">
                <span className="avatar-text">AO</span>
              </div>
              <span className="profile-name">Oluwatosin</span>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default UserDashboardHeader;
