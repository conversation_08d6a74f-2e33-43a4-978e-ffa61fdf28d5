/* User SideNav Container */
.user-sidenav-container {
  width: 280px;
  background: rgba(38, 38, 38, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  height: fit-content;
  position: sticky;
  top: 140px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 10;
}

.user-sidenav-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  position: relative;
  z-index: 11;
}

.user-sidenav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: none;
  border: none;
  border-radius: 8px;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-size: 15px;
  font-weight: 600;
  line-height: 1.5;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;
  position: relative;
  pointer-events: auto;
  z-index: 11;
}

.user-sidenav-item:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #FFFFFF;
}

.user-sidenav-item.active {
  background: #BF4129;
  color: #FFFFFF;
  box-shadow: 0 4px 16px rgba(191, 65, 41, 0.3);
}

.user-sidenav-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
  filter: brightness(0.8);
  transition: filter 0.3s ease;
}

.user-sidenav-item:hover .user-sidenav-icon,
.user-sidenav-item.active .user-sidenav-icon {
  filter: brightness(1);
}

.user-sidenav-label {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .user-sidenav-container {
    width: 100%;
    position: static;
    order: 1;
    top: auto;
    padding: 8px 12px;
    margin-bottom: 20px;
    margin-top: 10px !important; /* Shift sidenav down to add space */
    border-radius: 12px;
    height: 60px;
    display: flex;
    align-items: center;
    z-index: 10;
  }

  .user-sidenav-list {
    flex-direction: row;
    overflow-x: auto;
    gap: 6px; /* Use treasury dashboard gap */
    padding: 0; /* Use treasury dashboard padding */
    scrollbar-width: none;
    -ms-overflow-style: none;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: 100%;
    scroll-behavior: smooth;
  }

  .user-sidenav-list::-webkit-scrollbar {
    display: none;
  }

  .user-sidenav-item {
    white-space: nowrap;
    min-width: 110px; /* Use treasury dashboard min-width */
    max-width: 120px; /* Use treasury dashboard max-width */
    padding: 8px 12px; /* Use treasury dashboard padding */
    flex-shrink: 0;
    border-radius: 6px; /* Use treasury dashboard border-radius */
    font-size: 12px; /* Use treasury dashboard font-size */
    font-weight: 500;
    line-height: 1.2;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px; /* Use treasury dashboard gap */
    box-sizing: border-box;
    background: transparent;
    border: none;
    pointer-events: auto;
    cursor: pointer;
    z-index: 11;
  }

  .user-sidenav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border: none;
  }

  .user-sidenav-item.active {
    background: #BF4129;
    border: none;
  }

  .user-sidenav-icon {
    width: 18px; /* Use treasury dashboard icon size */
    height: 18px;
  }

  .user-sidenav-label {
    font-size: 11px; /* Use treasury dashboard label size */
  }
}
