# Landing Page Components

This folder contains standalone landing page components that were migrated from the `temp_nextjs_components` folder. These components are designed to be completely isolated from the main application's CSS and styling.

## Components

- **LandingPage.tsx** - Main landing page component that combines all other components
- **Header.tsx** - Navigation header with mobile menu support
- **HeroSection.tsx** - Hero section with call-to-action
- **ComparisonSection.tsx** - Comparison table between traditional crypto and ESVC
- **Footer.tsx** - Footer with links and social media icons
- **landing.css** - Standalone CSS file with all necessary styles

## Features

- **Standalone Styling**: All styles are scoped within `.landing-page-container` to prevent conflicts
- **React Router Integration**: Uses React Router's `Link` component instead of Next.js `Link`
- **Responsive Design**: Mobile-first responsive design with Tailwind-like utility classes
- **Isolated Dependencies**: No external CSS dependencies that could affect other parts of the app

## Usage

The landing page is accessible at `/landing` route in the main application.

```tsx
import { LandingPage } from './landing';

// In your router
<Route path="/landing" element={<LandingPage />} />
```

## Styling

The landing page uses its own CSS file (`landing.css`) that:
- Imports Google Fonts directly
- Defines CSS custom properties for colors and fonts
- Uses utility classes similar to Tailwind CSS
- All styles are scoped to prevent conflicts with the main app

## Images

Currently using placeholder images (`/api/placeholder/width/height`). Replace these with actual images as needed:
- Logo images
- Decorative elements
- Icons

## Migration Notes

- Converted from Next.js `Image` component to standard `img` tags
- Replaced Next.js `Link` with React Router `Link`
- Removed Next.js specific imports and directives
- Adapted styling to work without Tailwind CSS dependency
